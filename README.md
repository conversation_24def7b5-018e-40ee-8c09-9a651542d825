src/
|-- overlays/                # Main directory for overlays
|   |-- EtcOverlay/          # Specific overlay directory
|   |   |-- index.tsx        # Main component file
|   |   |-- styles.ts        # Specific styles for this overlay
|   |   |-- helpers.ts       # Helper functions specific to this overlay
|-- options/                 # Directory for options or settings page
|   |-- index.tsx            # Main component file for options page
|-- hooks/                  # Custom hooks
|   |-- useETC.ts            # Hook for ETC calculation
|   |-- useDOMData.ts        # Hook to fetch data from the DOM
|-- services/               # Services like storage or API calls
|   |-- storage.ts           # Functions related to Plasmo's storage library
|   |-- messaging.ts         # Functions related to Plasmo's messaging library
|   |-- api.ts               # Functions for any other API calls
|-- utils/                  # Utility functions
|   |-- timeCalculations.ts  # Time and date calculation functions
|   |-- domSelectors.ts      # Functions to fetch data from the DOM
|-- config/                 # Configuration files
|   |-- plasmoConfig.ts      # Configuration related to Plasmo
|-- types/                  # TypeScript types or interfaces
|   |-- index.ts             # Types like PlasmoCSConfig, etc.
|-- background.ts           # Background script for the extension
|-- .env                    # Environment variables for Plasmo


<!-- //Working hook.
import { useState, useEffect } from 'react';
import { useStorage } from "@plasmohq/storage/hook"
import dayjs from 'dayjs';
import 'dayjs/locale/nb'; // for Norwegian
import advancedFormat from 'dayjs/plugin/advancedFormat';
dayjs.extend(advancedFormat);

function useFetchDataOnElementPresence() {
  const [appointmentDate, setAppointmentDate] = useState<string | null>(null);
  const [rawFrt, setRawFrt] = useState<string | null>(null);
  const [rawScheduledFrt, setRawScheduledFrt] = useState<string | null>(null);

  // import from useStorage
  const [wOpeningHours] = useStorage("wOpeningHours")
  const [wClosingHours] = useStorage("wClosingHours")
  const [minimumHours] = useStorage("minimumHours")
  const [multiplier] = useStorage("multiplier")
  const [fohOpeningHours] = useStorage("fohOpeningHours")
  const [fohClosingHours] = useStorage("fohClosingHours")

  const fetchDataWhenElementPresent = () => {
    const targetSelector = "#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div.width-290 > div.width-100.sa-padding.ng-star-inserted > div > div:nth-child(2) > span";
    const targetElement = document.querySelector(targetSelector);
    
    if (targetElement) {
      // Disconnect observer once the target element is found
      observer.disconnect();

      // Fetch and format the appointment date
      const dateElement = document.querySelector("#sv-appt-datetime-link") as HTMLElement;
      if (dateElement) {
        const formattedDate = dayjs(dateElement.innerText.trim(), "ddd D MMM YYYY h:mm A").locale('nb').format('dddd D. MMMM YYYY [kl.] HH:mm');
        setAppointmentDate(formattedDate);
      }

      // Extract rawFrt value
      const rawFrtElement = document.querySelector("#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div.width-290 > div.width-100.sa-padding.ng-star-inserted > div > div:nth-child(1) > div") as HTMLElement;
      if (rawFrtElement) {
        const match = rawFrtElement.innerText.match(/(\d+\.\d+)h/);
        if (match) {
          setRawFrt(match[1]);
        }
      }

      // Extract rawScheduledFrt value
      const rawScheduledFrtElement = document.querySelector("#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div.width-290 > div.width-100.sa-padding.ng-star-inserted > div > div:nth-child(2) > div") as HTMLElement;
      if (rawScheduledFrtElement) {
        const match = rawScheduledFrtElement.innerText.match(/(\d+\.\d+)h/);
        if (match) {
          setRawScheduledFrt(match[1]);
        }
      }
    }
  };

  const observer = new MutationObserver(fetchDataWhenElementPresent);

  useEffect(() => {
    observer.observe(document.body, { childList: true, subtree: true });
    
    return () => {
      observer.disconnect();
    };
  }, []);

  return {
    appointmentDate,
    rawFrt,
    rawScheduledFrt
  };
}

export default useFetchDataOnElementPresence; -->
