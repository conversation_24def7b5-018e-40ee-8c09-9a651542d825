import dayjs from "dayjs"
import React, { useEffect } from "react"

import { useStorage } from "@plasmohq/storage/hook"

// Basic styles
const styles = {
  container: {
    maxWidth: "800px",
    margin: "0 auto",
    padding: "20px",
    fontFamily: "Arial, sans-serif",
    color: "#fff",
    backgroundColor: "#121212"
  },
  header: {
    fontSize: "24px",
    fontWeight: "bold",
    marginBottom: "20px",
    color: "#fff"
  },
  section: {
    marginBottom: "20px",
    padding: "15px",
    backgroundColor: "#1e1e1e",
    borderRadius: "4px"
  },
  sectionTitle: {
    fontSize: "16px",
    fontWeight: "bold",
    marginBottom: "10px",
    color: "#4dabf5"
  },
  formGroup: {
    marginBottom: "15px"
  },
  label: {
    display: "block",
    marginBottom: "5px",
    fontSize: "14px",
    color: "#ccc"
  },
  input: {
    width: "100%",
    padding: "8px",
    backgroundColor: "#333",
    border: "1px solid #444",
    borderRadius: "4px",
    color: "#fff",
    fontSize: "14px"
  },
  timeInputContainer: {
    display: "flex",
    gap: "10px"
  },
  timeInput: {
    width: "80px",
    padding: "8px",
    backgroundColor: "#333",
    border: "1px solid #444",
    borderRadius: "4px",
    color: "#fff",
    fontSize: "14px"
  },
  row: {
    display: "flex",
    flexWrap: "wrap",
    margin: "0 -10px"
  },
  col: {
    flex: "1 0 50%",
    padding: "0 10px",
    boxSizing: "border-box"
  },
  helperText: {
    fontSize: "12px",
    color: "#888",
    marginTop: "4px"
  },
  footer: {
    textAlign: "center",
    fontSize: "12px",
    color: "#666",
    marginTop: "20px"
  }
}

// Helper function to format time string to HH:MM
const formatTimeString = (isoString: string): string => {
  if (!isoString) return ""
  const date = new Date(isoString)
  return `${date.getHours().toString().padStart(2, "0")}:${date
    .getMinutes()
    .toString()
    .padStart(2, "0")}`
}

// Helper function to parse time string to ISO
const parseTimeToISO = (timeString: string): string => {
  const [hours, minutes] = timeString.split(":").map(Number)
  const date = new Date()
  date.setHours(hours, minutes, 0, 0)
  return date.toISOString()
}

export default function OptionsPage() {
  // Storage hooks with default values
  const [wOpeningHours, setwOpeningHours] = useStorage<string>(
    "wOpeningHours",
    "07:00"
  )
  const [wClosingHours, setwClosingHours] = useStorage<string>(
    "wClosingHours",
    "16:00"
  )
  const [fohOpeningHours, setFohOpeningHours] = useStorage<string>(
    "fohOpeningHours",
    "07:00"
  )
  const [fohClosingHours, setFohClosingHours] = useStorage<string>(
    "fohClosingHours",
    "16:00"
  )
  const [minimumHours, setMinimumHours] = useStorage<number>(
    "minimumHours",
    2.5
  )
  const [multiplier, setMultiplier] = useStorage<number>("multiplier", 1.2)

  // Set up body styles
  useEffect(() => {
    document.body.style.margin = "0"
    document.body.style.padding = "0"
    document.body.style.backgroundColor = "#121212"
  }, [])

  // Handle time input changes
  const handleTimeChange =
    (setter: (value: string) => void) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setter(parseTimeToISO(e.target.value))
    }

  // Handle number input changes
  const handleNumberChange =
    (setter: (value: number) => void) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setter(Number(e.target.value))
    }

  return (
    <div style={styles.container}>
      <h1 style={styles.header}>Tesla Service Assistant Options</h1>

      {/* Workshop Hours Section */}
      <div style={styles.section}>
        <h2 style={styles.sectionTitle}>Workshop Hours</h2>
        <div style={styles.row}>
          <div style={styles.col}>
            <div style={styles.formGroup}>
              <label style={styles.label}>Opening Time</label>
              <input
                type="time"
                style={styles.input}
                value={formatTimeString(wOpeningHours)}
                onChange={handleTimeChange(setwOpeningHours)}
              />
            </div>
          </div>
          <div style={styles.col}>
            <div style={styles.formGroup}>
              <label style={styles.label}>Closing Time</label>
              <input
                type="time"
                style={styles.input}
                value={formatTimeString(wClosingHours)}
                onChange={handleTimeChange(setwClosingHours)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Front of House Hours Section */}
      <div style={styles.section}>
        <h2 style={styles.sectionTitle}>Front of House Hours</h2>
        <div style={styles.row}>
          <div style={styles.col}>
            <div style={styles.formGroup}>
              <label style={styles.label}>Opening Time</label>
              <input
                type="time"
                style={styles.input}
                value={formatTimeString(fohOpeningHours)}
                onChange={handleTimeChange(setFohOpeningHours)}
              />
            </div>
          </div>
          <div style={styles.col}>
            <div style={styles.formGroup}>
              <label style={styles.label}>Closing Time</label>
              <input
                type="time"
                style={styles.input}
                value={formatTimeString(fohClosingHours)}
                onChange={handleTimeChange(setFohClosingHours)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Calculation Settings Section */}
      <div style={styles.section}>
        <h2 style={styles.sectionTitle}>Calculation Settings</h2>
        <div style={styles.row}>
          <div style={styles.col}>
            <div style={styles.formGroup}>
              <label style={styles.label}>Minimum Hours</label>
              <input
                type="number"
                style={styles.input}
                value={minimumHours || ""}
                onChange={handleNumberChange(setMinimumHours)}
                step="0.1"
                min="0"
              />
              <div style={styles.helperText}>
                Minimum repair duration in hours
              </div>
            </div>
          </div>
          <div style={styles.col}>
            <div style={styles.formGroup}>
              <label style={styles.label}>Multiplier</label>
              <input
                type="number"
                style={styles.input}
                value={multiplier || ""}
                onChange={handleNumberChange(setMultiplier)}
                step="0.1"
                min="0"
              />
              <div style={styles.helperText}>Factor to multiply FRT by</div>
            </div>
          </div>
        </div>

        <div style={styles.formGroup}>
          <label style={styles.label}>Preferred FRT (Coming Soon)</label>
          <select style={styles.input} disabled>
            <option value="10">Prefer FRT</option>
            <option value="20">Prefer Scheduled FRT</option>
            <option value="30">Choose Higher Value</option>
          </select>
          <div style={styles.helperText}>
            This feature is not yet implemented
          </div>
        </div>
      </div>

      <div style={styles.footer}>Tesla Service Assistant v1.0.0</div>
    </div>
  )
}
