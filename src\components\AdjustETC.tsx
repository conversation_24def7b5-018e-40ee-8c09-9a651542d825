import AddIcon from "@mui/icons-material/Add"
import RemoveIcon from "@mui/icons-material/Remove"
import RestartAltIcon from "@mui/icons-material/RestartAlt"
import {
  Box,
  <PERSON>ton,
  Card,
  Divider,
  Grid,
  IconButton,
  <PERSON>ack,
  Too<PERSON><PERSON>,
  Typography
} from "@mui/material"
import dayjs from "dayjs"
import React, { useCallback, useMemo } from "react"

import {
  useMemoWithPerformance,
  useThrottle
} from "../hooks/usePerformanceOptimization"
import logger from "../utils/logger"
import performance from "../utils/performance"

// Create a logger for this component
const log = logger.createLogger("AdjustETC")

// We've removed the updateTeslaUIField function as we only want to update the UI
// when the Set ETC button is clicked

interface AdjustETCProps {
  etc: dayjs.Dayjs | null
  originalEtc: dayjs.Dayjs | null
  duration: number | null
  setEtc: (etc: dayjs.Dayjs | null) => void
  setDuration: (duration: number | null) => void
}

const AdjustETC: React.FC<AdjustETCProps> = React.memo(
  ({ etc, originalEtc, duration, setEtc, setDuration }) => {
    // Log component render
    React.useEffect(() => {
      log.debug("AdjustETC component rendered", { etc, originalEtc, duration })
    }, [etc, originalEtc, duration])

    // Predefined time adjustments in minutes - memoized to prevent recreation
    const timeAdjustments = useMemoWithPerformance(
      () => [
        { label: "15m", minutes: 15 },
        { label: "1h", minutes: 60 },
        { label: "4h", minutes: 240 },
        { label: "1d", minutes: 1440 }
      ],
      [],
      "AdjustETC-timeAdjustments"
    )

    // Handle quick adjustment buttons - removed throttling for immediate response
    const handleQuickAdjust = useCallback(
      (minutes: number, operation: "add" | "subtract") => {
        performance.startMeasure(`adjustETC-${operation}-${minutes}`)
        log.debug(`Adjusting ETC: ${operation} ${minutes} minutes`, {
          currentEtc: etc,
          currentDuration: duration
        })

        try {
          if (etc) {
            const newEtc = etc[operation](minutes, "minute")
            setEtc(newEtc)

            // Update duration
            if (duration !== null) {
              const hourAdjustment = minutes / 60 // Convert minutes to hours
              const newDuration =
                operation === "add"
                  ? duration + hourAdjustment
                  : duration - hourAdjustment
              setDuration(newDuration)
            }

            // We no longer update the Tesla UI field immediately
            // The UI will only be updated when the Set ETC button is clicked

            log.debug("ETC adjustment complete", {
              newEtc: newEtc.format(),
              newDuration:
                duration !== null
                  ? operation === "add"
                    ? duration + minutes / 60
                    : duration - minutes / 60
                  : null
            })
          } else {
            log.warn("Cannot adjust ETC: ETC is null")
          }
        } catch (error) {
          log.error("Error adjusting ETC", error)
        } finally {
          performance.endMeasure(`adjustETC-${operation}-${minutes}`)
        }
      },
      [etc, duration, setEtc, setDuration]
    )

    // Reset to original ETC with performance tracking
    const handleReset = useCallback(() => {
      performance.startMeasure("resetETC")
      log.debug("Resetting ETC to original", { originalEtc })

      try {
        if (originalEtc) {
          setEtc(originalEtc)

          // We no longer update the Tesla UI field here
          // The UI will only be updated when the Set ETC button is clicked

          log.debug("ETC reset complete", { newEtc: originalEtc.format() })
        } else {
          log.warn("Cannot reset ETC: Original ETC is null")
        }
      } catch (error) {
        log.error("Error resetting ETC", error)
      } finally {
        performance.endMeasure("resetETC")
      }
    }, [originalEtc, setEtc])

    // Memoize the header section
    const HeaderSection = useMemoWithPerformance(
      () => (
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center"
          }}>
          <Typography
            variant="subtitle2"
            sx={{
              fontWeight: 600,
              color: "primary.light",
              fontSize: "0.75rem"
            }}>
            Adjust ETC
          </Typography>
          <Tooltip title="Reset to original ETC">
            <IconButton
              size="small"
              onClick={handleReset}
              sx={{ color: "warning.main", padding: "2px" }}>
              <RestartAltIcon sx={{ fontSize: "1rem" }} />
            </IconButton>
          </Tooltip>
        </Box>
      ),
      [handleReset],
      "AdjustETC-HeaderSection"
    )

    // Memoize the adjustment buttons
    const AdjustmentButtons = useMemoWithPerformance(
      () => (
        <Box>
          {timeAdjustments.map((adjustment) => (
            <Grid
              container
              spacing={0.5}
              key={adjustment.label}
              sx={{ mb: 0.5 }}>
              <Grid item xs={6}>
                <Button
                  size="small"
                  variant="contained"
                  color="primary"
                  fullWidth
                  onClick={() => handleQuickAdjust(adjustment.minutes, "add")}
                  startIcon={<AddIcon sx={{ fontSize: "0.9rem" }} />}
                  sx={{
                    fontWeight: 500,
                    boxShadow: 1,
                    fontSize: "0.7rem",
                    py: 0.5,
                    height: 28,
                    "&:hover": {
                      boxShadow: 2,
                      backgroundColor: "primary.dark"
                    }
                  }}>
                  {adjustment.label}
                </Button>
              </Grid>
              <Grid item xs={6}>
                <Button
                  size="small"
                  variant="outlined"
                  color="secondary"
                  fullWidth
                  onClick={() =>
                    handleQuickAdjust(adjustment.minutes, "subtract")
                  }
                  startIcon={<RemoveIcon sx={{ fontSize: "0.9rem" }} />}
                  sx={{
                    fontWeight: 500,
                    borderColor: "secondary.main",
                    color: "secondary.main",
                    fontSize: "0.7rem",
                    py: 0.5,
                    height: 28,
                    "&:hover": {
                      backgroundColor: "rgba(156, 39, 176, 0.08)"
                    }
                  }}>
                  {adjustment.label}
                </Button>
              </Grid>
            </Grid>
          ))}
        </Box>
      ),
      [timeAdjustments, handleQuickAdjust],
      "AdjustETC-AdjustmentButtons"
    )

    return (
      <Card
        variant="outlined"
        sx={{ p: 1, mb: 1, bgcolor: "background.paper" }}>
        <Stack spacing={1}>
          {HeaderSection}
          <Divider />
          {/* Quick adjustment buttons */}
          {AdjustmentButtons}
        </Stack>
      </Card>
    )
  }
)

export default AdjustETC
