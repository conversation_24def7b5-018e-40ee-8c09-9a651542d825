import dayjs from "dayjs"
import { useEffect, useRef, useState } from "react"

import { useStorage } from "@plasmohq/storage/hook"

import { calculateETC } from "../utils/calculateETC"

function useFetchDataOnElementPresence() {
  // State for appointment data
  const [appointmentDate, setAppointmentDate] = useState<dayjs.Dayjs | null>(
    null
  )
  const [rawFrt, setRawFrt] = useState<number | null>(null)
  const [rawScheduledFrt, setRawScheduledFrt] = useState<number | null>(null)
  const [etc, setEtc] = useState<dayjs.Dayjs | null>(null)
  const [duration, setDuration] = useState<number | null>(null)
  const [vin, setVin] = useState<string | null>(null)

  // Storage values
  const [wOpeningHours] = useStorage("wOpeningHours")
  const [wClosingHours] = useStorage("wClosingHours")
  const [minimumHours] = useStorage("minimumHours")
  const [multiplier] = useStorage("multiplier")
  const [fohOpeningHours] = useStorage("fohOpeningHours")
  const [fohClosingHours] = useStorage("fohClosingHours")

  // Refs to track data fetching and calculation status
  const dataFetchedRef = useRef<boolean>(false)
  const etcCalculatedRef = useRef<boolean>(false)
  const lastCalculationInputsRef = useRef<string>("")

  const fetchDataWhenElementPresent = () => {
    // If we've already successfully fetched data, don't fetch again
    if (dataFetchedRef.current) {
      return
    }

    const fetchInnerText = (selector: string): string | null => {
      const element = document.querySelector(selector) as HTMLElement
      return element ? element.innerText : null
    }

    const parseHours = (text: string | null): number => {
      if (text) {
        const decimalMatch = text.match(/(\d+\.\d+)h/)
        if (decimalMatch) {
          return parseFloat(decimalMatch[1])
        }

        const timeMatch = text.match(/(\d+):(\d+)/)
        if (timeMatch) {
          const hours = parseInt(timeMatch[1], 10)
          const minutes = parseInt(timeMatch[2], 10)
          return hours + minutes / 60
        }
      }
      return NaN
    }

    const apptDateText = fetchInnerText("#sv-appt-datetime-link")
    const rawFrtText = fetchInnerText(
      "#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div.width-290 > div.width-100.sa-padding.ng-star-inserted > div > div:nth-child(1) > div"
    )
    const rawScheduledFrtText = fetchInnerText(
      "#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div.width-290 > div.width-100.sa-padding.ng-star-inserted > div > div:nth-child(2) > div"
    )

    // Check if we have all the required data
    const hasAllData = apptDateText && (rawFrtText || rawScheduledFrtText)

    if (hasAllData) {
      const parsedRawFrt = parseHours(rawFrtText)
      const validRawFrt = !isNaN(parsedRawFrt)

      const parsedRawScheduledFrt = parseHours(rawScheduledFrtText)
      const validRawScheduledFrt = !isNaN(parsedRawScheduledFrt)

      const formattedDate = dayjs(apptDateText, "ddd DD MMM YYYY h:mm A")
      const validDate = formattedDate.isValid()

      // Only update state if we have valid data
      if ((validRawFrt || validRawScheduledFrt) && validDate) {
        console.log("Valid appointment data found, setting state...")

        if (validRawFrt) {
          setRawFrt(parsedRawFrt)
          console.log(`Raw FRT: ${parsedRawFrt}`)
        }

        if (validRawScheduledFrt) {
          setRawScheduledFrt(parsedRawScheduledFrt)
          console.log(`Raw Scheduled FRT: ${parsedRawScheduledFrt}`)
        }

        if (validDate) {
          setAppointmentDate(formattedDate)
          console.log(
            `Appointment Date: ${formattedDate.format("YYYY-MM-DD HH:mm")}`
          )
        }

        // Mark data as fetched to prevent redundant fetches
        dataFetchedRef.current = true
      }
    }

    // Only fetch VIN if we haven't already found one
    if (!vin) {
      // Fetch and set VIN - try multiple selectors to be more robust
      let vinFound = false

      // Try the primary selector first
      const vinElement = document.querySelector(
        "#mat-select-value-7 > span > span"
      ) as HTMLElement

      if (vinElement && vinElement.innerText) {
        const fullText = vinElement.innerText.trim()
        // Check if the text contains a valid VIN (17 alphanumeric characters)
        const vinMatch = fullText.match(/[A-HJ-NPR-Z0-9]{17}/i)
        if (vinMatch) {
          console.log(`VIN found using primary selector: ${vinMatch[0]}`)
          setVin(vinMatch[0])
          vinFound = true
        } else if (fullText.length >= 17) {
          // Fallback to extracting the last 17 characters if they look like a VIN
          const possibleVin = fullText.slice(-17)
          if (/^[A-HJ-NPR-Z0-9]{17}$/i.test(possibleVin)) {
            console.log(
              `VIN extracted from text using primary selector: ${possibleVin}`
            )
            setVin(possibleVin)
            vinFound = true
          }
        }
      }

      // If VIN not found with primary selector, try alternative selectors
      if (!vinFound) {
        // Try to find VIN in the page title
        const title = document.title
        const titleVinMatch = title.match(/[A-HJ-NPR-Z0-9]{17}/i)
        if (titleVinMatch) {
          console.log(`VIN found in page title: ${titleVinMatch[0]}`)
          setVin(titleVinMatch[0])
          vinFound = true
        }

        // Try other potential selectors if needed
        if (!vinFound) {
          // Look for any element containing a VIN-like string
          const allElements = document.querySelectorAll(
            "span, div, p, a, h1, h2, h3, h4, h5, h6"
          )
          for (const element of allElements) {
            const text = element.textContent || ""
            const vinMatch = text.match(/[A-HJ-NPR-Z0-9]{17}/i)
            if (vinMatch) {
              console.log(`VIN found in element: ${vinMatch[0]}`)
              setVin(vinMatch[0])
              vinFound = true
              break
            }
          }
        }
      }

      if (!vinFound) {
        console.warn("Could not find VIN on the page")
      }
    }
  }

  useEffect(() => {
    const observer = new MutationObserver(fetchDataWhenElementPresent)
    const timeoutId = setTimeout(() => {
      observer.observe(document.body, { childList: true, subtree: true })
    }, 3000)

    return () => {
      clearTimeout(timeoutId)
      observer.disconnect()
    }
  }, [])

  useEffect(() => {
    // Only calculate ETC if we have the necessary data and haven't calculated it already
    // or if the inputs have changed
    if (appointmentDate && (rawFrt || rawScheduledFrt)) {
      // Create a string representation of all inputs to detect changes
      const currentInputs = JSON.stringify({
        appointmentDate: appointmentDate.format("YYYY-MM-DD HH:mm"),
        rawFrt,
        rawScheduledFrt,
        wOpeningHours,
        wClosingHours,
        fohOpeningHours,
        fohClosingHours,
        multiplier,
        minimumHours
      })

      // Check if inputs have changed since last calculation
      if (currentInputs !== lastCalculationInputsRef.current) {
        console.log("Calculating ETC with new inputs...")

        // Update the last calculation inputs reference
        lastCalculationInputsRef.current = currentInputs

        // Calculate adjusted FRT
        const adjustedFrt =
          Math.max(rawFrt ?? 0, rawScheduledFrt ?? 0) * multiplier
        const repairDuration =
          adjustedFrt < minimumHours ? minimumHours : adjustedFrt

        // Only log the first time or when inputs change
        if (!etcCalculatedRef.current) {
          console.log(`Repair duration: ${repairDuration} hours`)
          etcCalculatedRef.current = true
        }

        // Calculate ETC
        const computedETC = calculateETC(
          appointmentDate,
          repairDuration,
          wOpeningHours,
          wClosingHours,
          fohOpeningHours,
          fohClosingHours
        )

        // Update state with computed values
        setEtc(computedETC)

        if (computedETC) {
          // Simple calculation: just get the total hours between appointment and ETC
          // This is what users expect to see - the total elapsed time
          const totalHours = computedETC.diff(appointmentDate, "hour")
          const totalMinutes = computedETC.diff(appointmentDate, "minute") % 60

          // Set the duration in hours
          setDuration(totalHours + totalMinutes / 60)

          console.log(
            `Duration calculation: Total hours: ${totalHours}, Additional minutes: ${totalMinutes}`
          )
          console.log(
            `From ${appointmentDate.format(
              "YYYY-MM-DD HH:mm"
            )} to ${computedETC.format("YYYY-MM-DD HH:mm")}`
          )
        } else {
          console.warn("Computed ETC is null. Unable to calculate duration.")
        }
      } else {
        console.log("Skipping ETC calculation - inputs haven't changed")
      }
    }
  }, [
    appointmentDate,
    rawFrt,
    rawScheduledFrt,
    wOpeningHours,
    wClosingHours,
    fohOpeningHours,
    fohClosingHours,
    multiplier,
    minimumHours
  ])

  return { appointmentDate, rawFrt, rawScheduledFrt, etc, duration, vin }
}

export default useFetchDataOnElementPresence
