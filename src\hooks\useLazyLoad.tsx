import React, { lazy, Suspense, ComponentType, LazyExoticComponent } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import ErrorBoundary from '../components/ErrorBoundary';

// Default loading component
const DefaultLoading = () => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      p: 3
    }}>
    <CircularProgress size={24} sx={{ mb: 1 }} />
    <Typography variant="body2" color="text.secondary">
      Loading...
    </Typography>
  </Box>
);

// Default error component
const DefaultError = ({ error, retry }: { error: Error; retry: () => void }) => (
  <Box
    sx={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      p: 3,
      color: 'error.main'
    }}>
    <Typography variant="body2" sx={{ mb: 1 }}>
      Failed to load component: {error.message}
    </Typography>
    <button onClick={retry}>Retry</button>
  </Box>
);

/**
 * Options for lazy loading components
 */
interface LazyLoadOptions {
  fallback?: React.ReactNode;
  errorComponent?: ComponentType<{ error: Error; retry: () => void }>;
  minDelay?: number; // Minimum delay before showing the component (to prevent flashing)
}

/**
 * Create a lazy-loaded component with error handling and loading state
 * @param factory Function that imports the component
 * @param options Options for lazy loading
 * @returns Lazy-loaded component
 */
export function createLazyComponent<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> & { preload: () => Promise<T> } {
  const {
    fallback = <DefaultLoading />,
    errorComponent: ErrorComponent = DefaultError,
    minDelay = 0
  } = options;

  // Create the lazy component
  const LazyComponent = lazy(() => {
    // Add minimum delay if specified
    if (minDelay > 0) {
      return Promise.all([
        factory(),
        new Promise(resolve => setTimeout(resolve, minDelay))
      ]).then(([moduleExports]) => moduleExports);
    }
    return factory();
  });

  // Create a wrapper component with Suspense and ErrorBoundary
  const WrappedComponent = (props: any) => (
    <ErrorBoundary
      fallback={({ error, resetErrorBoundary }) => (
        <ErrorComponent error={error} retry={resetErrorBoundary} />
      )}>
      <Suspense fallback={fallback}>
        <LazyComponent {...props} />
      </Suspense>
    </ErrorBoundary>
  );

  // Add preload method
  const preloadableComponent = WrappedComponent as any;
  preloadableComponent.preload = () => factory().then(module => module.default);

  return preloadableComponent;
}

/**
 * Hook to create a lazy-loaded component
 * @param factory Function that imports the component
 * @param options Options for lazy loading
 * @returns Lazy-loaded component
 */
export function useLazyComponent<T extends ComponentType<any>>(
  factory: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> & { preload: () => Promise<T> } {
  // This is a simple wrapper around createLazyComponent
  // In a real app, you might add more functionality here
  return React.useMemo(() => createLazyComponent(factory, options), []);
}

export default {
  createLazyComponent,
  useLazyComponent
};
