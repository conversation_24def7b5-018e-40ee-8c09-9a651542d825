import AddIcon from "@mui/icons-material/Add"
import RemoveIcon from "@mui/icons-material/Remove"
import { Box, Button, Grid, Tooltip } from "@mui/material"
import dayjs from "dayjs"
import React, { useCallback, useMemo } from "react"

import {
  useMemoWithPerformance,
  useThrottle
} from "../hooks/usePerformanceOptimization"
import logger from "../utils/logger"
import performance from "../utils/performance"

// Create a logger for this component
const log = logger.createLogger("AddTime")

interface AddTimeProps {
  etc: dayjs.Dayjs | null
  duration: number | null
  setEtc: (etc: dayjs.Dayjs | null) => void
  setDuration: (duration: number | null) => void
  buttonLabel: string
  minutes: number
}

const AddTime: React.FC<AddTimeProps> = React.memo(
  ({ etc, duration, setEtc, setDuration, buttonLabel, minutes }) => {
    // Use throttled function to prevent rapid clicks
    const handleTimeChange = useThrottle((operation: "add" | "subtract") => {
      performance.startMeasure(`handleTimeChange-${operation}-${minutes}`)
      log.debug(`Time change: ${operation} ${minutes} minutes`, {
        currentEtc: etc,
        currentDuration: duration
      })

      try {
        // Calculate the new etc time based on operation
        const newEtc = etc ? etc[operation](minutes, "minute") : null

        // Calculate the new duration, also based on operation
        const newDuration =
          duration !== null
            ? operation === "add"
              ? duration + minutes
              : duration - minutes
            : null

        if (newEtc) {
          setEtc(newEtc)
        }

        if (newDuration !== null) {
          setDuration(newDuration)
        }

        log.debug(`Time change complete`, { newEtc, newDuration })
      } catch (error) {
        log.error(`Error in handleTimeChange`, error)
      } finally {
        performance.endMeasure(`handleTimeChange-${operation}-${minutes}`)
      }
    }, 300) // Throttle to 300ms to prevent rapid clicks

    // Memoize button styles to prevent unnecessary recalculations
    const commonButtonStyles = useMemoWithPerformance(
      () => ({
        fontWeight: 500,
        height: 36, // Fixed height for all buttons
        whiteSpace: "nowrap", // Prevent text wrapping
        minWidth: 0, // Allow buttons to shrink if needed
        px: 1, // Reduce horizontal padding to fit text better
        "& .MuiButton-startIcon": {
          marginRight: "4px" // Reduce space between icon and text
        },
        "& .MuiSvgIcon-root": {
          fontSize: "1.2rem" // Make icons slightly smaller
        }
      }),
      [],
      "AddTime-buttonStyles"
    )

    // Memoize the add button
    const AddButton = useMemoWithPerformance(
      () => (
        <Grid item xs={6}>
          <Tooltip title={`Add ${buttonLabel}`} arrow placement="top">
            <Button
              size="small"
              variant="contained"
              color="primary"
              fullWidth
              onClick={() => handleTimeChange("add")}
              startIcon={<AddIcon />}
              sx={{
                ...commonButtonStyles,
                boxShadow: 1,
                "&:hover": {
                  boxShadow: 2,
                  backgroundColor: "primary.dark"
                }
              }}>
              {buttonLabel}
            </Button>
          </Tooltip>
        </Grid>
      ),
      [buttonLabel, commonButtonStyles, handleTimeChange],
      "AddTime-AddButton"
    )

    // Memoize the subtract button
    const SubtractButton = useMemoWithPerformance(
      () => (
        <Grid item xs={6}>
          <Tooltip title={`Subtract ${buttonLabel}`} arrow placement="top">
            <Button
              size="small"
              variant="outlined"
              color="secondary"
              fullWidth
              onClick={() => handleTimeChange("subtract")}
              startIcon={<RemoveIcon />}
              sx={{
                ...commonButtonStyles,
                "&:hover": {
                  backgroundColor: "rgba(156, 39, 176, 0.08)"
                }
              }}>
              {buttonLabel}
            </Button>
          </Tooltip>
        </Grid>
      ),
      [buttonLabel, commonButtonStyles, handleTimeChange],
      "AddTime-SubtractButton"
    )

    // Log render for performance tracking
    React.useEffect(() => {
      log.debug(`AddTime rendered with minutes: ${minutes}`)
      return () => {
        log.debug(`AddTime unmounted with minutes: ${minutes}`)
      }
    }, [minutes])

    return (
      <Box sx={{ mb: 1 }}>
        <Grid container spacing={1}>
          {AddButton}
          {SubtractButton}
        </Grid>
      </Box>
    )
  }
)

export default AddTime
