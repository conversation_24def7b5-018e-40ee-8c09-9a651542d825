import dayjs from "dayjs"
import duration from "dayjs/plugin/duration"
import isSameOrAfter from "dayjs/plugin/isSameOrAfter"
import isSameOrBefore from "dayjs/plugin/isSameOrBefore"
import weekday from "dayjs/plugin/weekday"

// Extend dayjs with required plugins
dayjs.extend(weekday)
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)
dayjs.extend(duration)

/**
 * Checks if a date falls on a weekend (Saturday or Sunday)
 * @param date The date to check
 * @returns True if the date is a weekend day, false otherwise
 */
function isWeekend(date: dayjs.Dayjs): boolean {
  const day = date.day()
  return day === 0 || day === 6 // 0 is Sunday, 6 is Saturday
}

/**
 * Adjusts a date to the next business day if it falls on a weekend
 * @param date The date to adjust
 * @returns The adjusted date (same date if not a weekend, or next Monday if weekend)
 */
function adjustToNextBusinessDay(date: dayjs.Dayjs): dayjs.Dayjs {
  if (!isWeekend(date)) {
    return date
  }

  // If it's a weekend, move to the next Monday
  const daysToAdd = date.day() === 0 ? 1 : 2 // Sunday: add 1 day, Saturday: add 2 days
  return date.add(daysToAdd, "day")
}

/**
 * Adjusts a date to business hours for a given day
 * @param date The date to adjust
 * @param openingHours The opening hours string
 * @param closingHours The closing hours string
 * @returns The date adjusted to business hours
 */
function adjustToBusinessHours(
  date: dayjs.Dayjs,
  openingHours: string,
  closingHours: string
): dayjs.Dayjs {
  // Create opening and closing time objects for the same day as the input date
  const open = date
    .clone()
    .hour(dayjs(openingHours).hour())
    .minute(dayjs(openingHours).minute())
    .second(0)
    .millisecond(0)

  const close = date
    .clone()
    .hour(dayjs(closingHours).hour())
    .minute(dayjs(closingHours).minute())
    .second(0)
    .millisecond(0)

  // Case 1: If the date is before opening hours, move to opening time
  if (date.isBefore(open)) {
    return open
  }
  // Case 2: If the date is after closing hours, move to next business day's opening time
  else if (date.isAfter(close)) {
    // First add 1 day
    let nextDay = date.clone().add(1, "day")

    // Then check if it's a weekend and adjust accordingly
    nextDay = adjustToNextBusinessDay(nextDay)

    // Set to opening hours
    nextDay = nextDay
      .hour(dayjs(openingHours).hour())
      .minute(dayjs(openingHours).minute())
      .second(0)
      .millisecond(0)

    return nextDay
  }
  // Case 3: Date is within business hours, no adjustment needed
  else {
    return date
  }
}

/**
 * Calculates the estimated time of completion (ETC) for a repair
 * @param appointmentDate The appointment start date
 * @param repairDuration The repair duration in hours
 * @param wOpeningHours Workshop opening hours
 * @param wClosingHours Workshop closing hours
 * @param fohOpeningHours Front of house opening hours
 * @param fohClosingHours Front of house closing hours
 * @returns The calculated ETC or null if input is invalid
 */
export function calculateETC(
  appointmentDate: dayjs.Dayjs | null,
  repairDuration: number,
  wOpeningHours: string,
  wClosingHours: string,
  fohOpeningHours: string,
  fohClosingHours: string
): dayjs.Dayjs | null {
  // Validate input
  if (!appointmentDate || !appointmentDate.isValid()) {
    console.error("Invalid appointment date provided.")
    return null
  }

  // Create a unique calculation ID to group logs
  const calcId = Math.random().toString(36).substring(2, 8)

  // Helper function to log only once per calculation
  const log = (message: string) => {
    console.log(`[ETC-${calcId}] ${message}`)
  }

  log(
    `Calculating ETC for appointment: ${appointmentDate.format(
      "YYYY-MM-DD HH:mm"
    )} (${appointmentDate.format("dddd")})`
  )
  log(`Repair duration: ${repairDuration} hours`)

  // Step 1: Calculate the raw ETC by adding the repair duration
  let currentETC = appointmentDate.add(repairDuration, "hour")
  log(
    `Raw ETC after adding repair duration: ${currentETC.format(
      "YYYY-MM-DD HH:mm"
    )} (${currentETC.format("dddd")})`
  )

  // Step 2: Handle multi-day repairs that span across closing hours
  const workshopOpen = dayjs(appointmentDate)
    .hour(dayjs(wOpeningHours).hour())
    .minute(dayjs(wOpeningHours).minute())

  const workshopClose = dayjs(appointmentDate)
    .hour(dayjs(wClosingHours).hour())
    .minute(dayjs(wClosingHours).minute())

  // Calculate available work hours in the first day
  let availableHoursFirstDay = 0
  if (appointmentDate.isBefore(workshopClose)) {
    availableHoursFirstDay = workshopClose.diff(appointmentDate, "hour", true)
    availableHoursFirstDay = Math.max(0, availableHoursFirstDay)
  }

  log(`Available hours on first day: ${availableHoursFirstDay}`)

  // If repair duration exceeds available hours on the first day
  if (repairDuration > availableHoursFirstDay) {
    log(`Repair spans multiple days`)

    // Calculate how many full workdays are needed
    const workdayHours = workshopClose.diff(workshopOpen, "hour", true)
    const remainingHours = repairDuration - availableHoursFirstDay
    const fullWorkdays = Math.floor(remainingHours / workdayHours)
    const partialDayHours = remainingHours % workdayHours

    log(`Workday hours: ${workdayHours}, Remaining hours: ${remainingHours}`)
    log(
      `Full workdays needed: ${fullWorkdays}, Partial day hours: ${partialDayHours}`
    )

    // Start with the end of the first day
    let adjustedETC = workshopClose.clone()

    // Add full workdays, skipping weekends
    for (let i = 0; i < fullWorkdays; i++) {
      adjustedETC = adjustedETC.add(1, "day")

      // Skip weekends
      while (isWeekend(adjustedETC)) {
        adjustedETC = adjustedETC.add(1, "day")
      }
    }

    // Add partial day hours
    if (partialDayHours > 0) {
      // Move to the next day's opening time
      adjustedETC = adjustedETC
        .add(1, "day")
        .hour(dayjs(wOpeningHours).hour())
        .minute(dayjs(wOpeningHours).minute())

      // Skip weekends
      while (isWeekend(adjustedETC)) {
        adjustedETC = adjustedETC.add(1, "day")
      }

      // Add the remaining hours
      adjustedETC = adjustedETC.add(partialDayHours, "hour")
    }

    currentETC = adjustedETC
    log(
      `Adjusted ETC after multi-day calculation: ${currentETC.format(
        "YYYY-MM-DD HH:mm"
      )} (${currentETC.format("dddd")})`
    )
  }

  // Step 3: Adjust to workshop business hours
  currentETC = adjustToBusinessHours(currentETC, wOpeningHours, wClosingHours)
  log(
    `ETC after workshop hours adjustment: ${currentETC.format(
      "YYYY-MM-DD HH:mm"
    )} (${currentETC.format("dddd")})`
  )

  // Step 4: Handle weekend adjustments - if it's a weekend, move to Monday
  if (isWeekend(currentETC)) {
    const nextMonday = currentETC.day(1 + 7) // Next Monday (day 1 + 7 to ensure it's the next week)
    currentETC = nextMonday
      .hour(dayjs(wOpeningHours).hour())
      .minute(dayjs(wOpeningHours).minute())
    log(
      `Weekend detected, moved to next Monday: ${currentETC.format(
        "YYYY-MM-DD HH:mm"
      )} (${currentETC.format("dddd")})`
    )
  }

  // Step 5: Adjust to front of house business hours
  currentETC = adjustToBusinessHours(
    currentETC,
    fohOpeningHours,
    fohClosingHours
  )
  log(
    `ETC after front of house hours adjustment: ${currentETC.format(
      "YYYY-MM-DD HH:mm"
    )} (${currentETC.format("dddd")})`
  )

  // Step 6: Round to the next quarter hour
  const roundedETC = roundToNextQuarter(currentETC)
  log(
    `Final ETC after rounding: ${roundedETC.format(
      "YYYY-MM-DD HH:mm"
    )} (${roundedETC.format("dddd")})`
  )

  return roundedETC
}

/**
 * Rounds a time to the next quarter hour (15, 30, 45, or 00)
 * @param date The date to round
 * @returns The date rounded to the next quarter hour
 */
function roundToNextQuarter(date: dayjs.Dayjs): dayjs.Dayjs {
  // Set seconds and milliseconds to zero
  date = date.set("second", 0).set("millisecond", 0)

  const minutes = date.minute()
  let additionalMinutes = 0

  // Calculate how many minutes to add to reach the next quarter hour
  if (minutes < 15) {
    additionalMinutes = 15 - minutes
  } else if (minutes < 30) {
    additionalMinutes = 30 - minutes
  } else if (minutes < 45) {
    additionalMinutes = 45 - minutes
  } else {
    additionalMinutes = 60 - minutes
  }

  // We don't need to log this separately as it's already logged in the main function
  return date.add(additionalMinutes, "minute")
}
