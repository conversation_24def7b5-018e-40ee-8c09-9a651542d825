import { createTheme } from "@mui/material/styles";

// Define a modern color palette
const colors = {
  primary: {
    main: "#1976d2",
    light: "#42a5f5",
    dark: "#1565c0",
    contrastText: "#ffffff"
  },
  secondary: {
    main: "#9c27b0",
    light: "#ba68c8",
    dark: "#7b1fa2",
    contrastText: "#ffffff"
  },
  success: {
    main: "#2e7d32",
    light: "#4caf50",
    dark: "#1b5e20",
    contrastText: "#ffffff"
  },
  error: {
    main: "#d32f2f",
    light: "#ef5350",
    dark: "#c62828",
    contrastText: "#ffffff"
  },
  warning: {
    main: "#ed6c02",
    light: "#ff9800",
    dark: "#e65100",
    contrastText: "#ffffff"
  },
  info: {
    main: "#0288d1",
    light: "#03a9f4",
    dark: "#01579b",
    contrastText: "#ffffff"
  },
  background: {
    default: "#121212",
    paper: "#1e1e1e"
  },
  text: {
    primary: "#ffffff",
    secondary: "rgba(255, 255, 255, 0.7)",
    disabled: "rgba(255, 255, 255, 0.5)"
  }
};

// Create a modern theme with rounded corners and consistent spacing
export const theme = createTheme({
  palette: {
    mode: "dark",
    ...colors
  },
  typography: {
    fontFamily: "'Roboto', 'Helvetica', 'Arial', sans-serif",
    h1: {
      fontSize: "2rem",
      fontWeight: 500
    },
    h2: {
      fontSize: "1.75rem",
      fontWeight: 500
    },
    h3: {
      fontSize: "1.5rem",
      fontWeight: 500
    },
    h4: {
      fontSize: "1.25rem",
      fontWeight: 500
    },
    h5: {
      fontSize: "1.1rem",
      fontWeight: 500
    },
    h6: {
      fontSize: "1rem",
      fontWeight: 500
    },
    subtitle1: {
      fontSize: "0.875rem",
      fontWeight: 400
    },
    subtitle2: {
      fontSize: "0.8rem",
      fontWeight: 500
    },
    body1: {
      fontSize: "0.875rem"
    },
    body2: {
      fontSize: "0.8rem"
    },
    button: {
      textTransform: "none",
      fontWeight: 500
    },
    caption: {
      fontSize: "0.75rem"
    }
  },
  shape: {
    borderRadius: 8
  },
  spacing: 8,
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: "none",
          fontWeight: 500,
          padding: "6px 16px"
        },
        sizeSmall: {
          padding: "4px 10px",
          fontSize: "0.8125rem"
        }
      }
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: "0 4px 20px 0 rgba(0,0,0,0.1), 0 7px 10px -5px rgba(0,0,0,0.2)"
        }
      }
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 16,
          "&:last-child": {
            paddingBottom: 16
          }
        }
      }
    },
    MuiCardActions: {
      styleOverrides: {
        root: {
          padding: "8px 16px 16px"
        }
      }
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 8
        }
      }
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          color: colors.text.primary
        }
      }
    }
  }
});

export default theme;
