/**
 * Formats a duration in hours to a human-readable string
 * @param durationInHours Duration in hours (can be decimal)
 * @returns Formatted string like "83h 30min"
 */
export const formatDuration = (durationInHours: number) => {
  // Convert to total minutes first
  const totalMinutes = Math.round(durationInHours * 60)

  // Calculate hours and remaining minutes
  const hours = Math.floor(totalMinutes / 60)
  const minutes = totalMinutes % 60

  // Format the string
  return `${hours}h ${minutes}min`
}
