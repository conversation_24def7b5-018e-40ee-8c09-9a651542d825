/**
 * Error handling utilities for consistent error management
 */
import logger from './logger';

// Error categories
export enum ErrorCategory {
  API = 'API_ERROR',
  NETWORK = 'NETWORK_ERROR',
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTH_ERROR',
  UNKNOWN = 'UNKNOWN_ERROR'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Interface for structured error information
export interface ErrorInfo {
  message: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  originalError?: any;
  context?: Record<string, any>;
  timestamp: Date;
}

// Create a structured error object
export function createErrorInfo(
  message: string,
  category: ErrorCategory = ErrorCategory.UNKNOWN,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  originalError?: any,
  context?: Record<string, any>
): ErrorInfo {
  return {
    message,
    category,
    severity,
    originalError,
    context,
    timestamp: new Date()
  };
}

// Handle an error with consistent logging and optional reporting
export function handleError(errorInfo: ErrorInfo): void {
  // Log the error
  logger.error(`${errorInfo.category}: ${errorInfo.message}`, {
    severity: errorInfo.severity,
    context: errorInfo.context,
    originalError: errorInfo.originalError
  });

  // For critical errors, we might want to report to a monitoring service
  if (errorInfo.severity === ErrorSeverity.CRITICAL) {
    // TODO: Implement error reporting to a service like Sentry
    console.error('CRITICAL ERROR:', errorInfo);
  }
}

// Utility to safely execute a function and handle any errors
export async function tryCatch<T>(
  fn: () => Promise<T>,
  errorMessage: string,
  category: ErrorCategory = ErrorCategory.UNKNOWN,
  severity: ErrorSeverity = ErrorSeverity.MEDIUM,
  context?: Record<string, any>
): Promise<T | null> {
  try {
    return await fn();
  } catch (error) {
    handleError(
      createErrorInfo(errorMessage, category, severity, error, context)
    );
    return null;
  }
}

// Create a wrapped API call function that handles errors consistently
export function createApiErrorHandler<T>(
  apiCall: (...args: any[]) => Promise<T>,
  errorMessage: string,
  context?: Record<string, any>
) {
  return async (...args: any[]): Promise<T | null> => {
    return tryCatch(
      () => apiCall(...args),
      errorMessage,
      ErrorCategory.API,
      ErrorSeverity.MEDIUM,
      { ...context, args }
    );
  };
}

export default {
  createErrorInfo,
  handleError,
  tryCatch,
  createApiErrorHandler,
  ErrorCategory,
  ErrorSeverity
};
