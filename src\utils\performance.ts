/**
 * Performance monitoring utilities
 */
import logger from './logger';

// Store performance marks
const marks: Record<string, number> = {};

// Store performance measurements
const measurements: Record<string, { duration: number; count: number }> = {};

/**
 * Start timing an operation
 * @param markName Name of the performance mark
 */
export function startMeasure(markName: string): void {
  marks[markName] = performance.now();
}

/**
 * End timing an operation and record the measurement
 * @param markName Name of the performance mark
 * @param logLevel Optional log level to log the measurement
 * @returns Duration in milliseconds
 */
export function endMeasure(markName: string, shouldLog = true): number {
  if (!marks[markName]) {
    logger.warn(`No start mark found for "${markName}"`);
    return 0;
  }

  const startTime = marks[markName];
  const endTime = performance.now();
  const duration = endTime - startTime;

  // Store the measurement
  if (!measurements[markName]) {
    measurements[markName] = { duration, count: 1 };
  } else {
    measurements[markName].duration += duration;
    measurements[markName].count += 1;
  }

  // Clean up the mark
  delete marks[markName];

  // Log the measurement if requested
  if (shouldLog) {
    logger.debug(`Performance: ${markName} took ${duration.toFixed(2)}ms`);
  }

  return duration;
}

/**
 * Get the average duration of a measurement
 * @param markName Name of the performance mark
 * @returns Average duration in milliseconds, or 0 if no measurements
 */
export function getAverageDuration(markName: string): number {
  const measurement = measurements[markName];
  if (!measurement || measurement.count === 0) return 0;
  return measurement.duration / measurement.count;
}

/**
 * Get all performance measurements
 * @returns Object with all measurements
 */
export function getAllMeasurements(): Record<string, { total: number; average: number; count: number }> {
  const result: Record<string, { total: number; average: number; count: number }> = {};
  
  for (const [name, data] of Object.entries(measurements)) {
    result[name] = {
      total: data.duration,
      average: data.duration / data.count,
      count: data.count
    };
  }
  
  return result;
}

/**
 * Reset all performance measurements
 */
export function resetMeasurements(): void {
  for (const key in measurements) {
    delete measurements[key];
  }
}

/**
 * Decorator for measuring function execution time
 * @param target The class prototype
 * @param propertyKey The method name
 * @param descriptor The property descriptor
 */
export function measure(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
  const originalMethod = descriptor.value;
  
  descriptor.value = function(...args: any[]) {
    const markName = `${target.constructor.name}.${propertyKey}`;
    startMeasure(markName);
    const result = originalMethod.apply(this, args);
    
    // Handle promises
    if (result instanceof Promise) {
      return result.finally(() => {
        endMeasure(markName);
      });
    }
    
    endMeasure(markName);
    return result;
  };
  
  return descriptor;
}

export default {
  startMeasure,
  endMeasure,
  getAverageDuration,
  getAllMeasurements,
  resetMeasurements,
  measure
};
