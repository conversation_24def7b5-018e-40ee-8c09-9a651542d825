/**
 * API caching utility to reduce redundant network requests
 */
import logger from './logger';
import performance from './performance';

// Cache entry interface
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiresAt: number;
}

// Cache configuration
interface CacheConfig {
  defaultTTL: number; // Time to live in milliseconds
  maxSize: number; // Maximum number of entries in the cache
  enabled: boolean; // Whether caching is enabled
}

// Default configuration
const DEFAULT_CONFIG: CacheConfig = {
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  maxSize: 100,
  enabled: true
};

// Cache storage
const cache: Map<string, CacheEntry<any>> = new Map();
let config: CacheConfig = { ...DEFAULT_CONFIG };

// Configure the cache
export function configureCache(options: Partial<CacheConfig>): void {
  config = { ...config, ...options };
  logger.debug('API cache configured', config);
}

// Generate a cache key from the request details
export function generateCacheKey(url: string, params?: any): string {
  if (!params) return url;
  const sortedParams = Object.keys(params)
    .sort()
    .reduce((result: Record<string, any>, key: string) => {
      result[key] = params[key];
      return result;
    }, {});
  
  return `${url}:${JSON.stringify(sortedParams)}`;
}

// Set a value in the cache
export function setCacheValue<T>(key: string, data: T, ttl?: number): void {
  if (!config.enabled) return;
  
  performance.startMeasure(`cache-set-${key}`);
  
  const now = Date.now();
  const expiresAt = now + (ttl || config.defaultTTL);
  
  // Ensure we don't exceed the max size
  if (cache.size >= config.maxSize) {
    // Remove the oldest entry
    const oldestKey = Array.from(cache.keys())[0];
    cache.delete(oldestKey);
    logger.debug(`Cache full, removed oldest entry: ${oldestKey}`);
  }
  
  cache.set(key, {
    data,
    timestamp: now,
    expiresAt
  });
  
  performance.endMeasure(`cache-set-${key}`);
  logger.debug(`Cached data for key: ${key}, expires in ${(ttl || config.defaultTTL) / 1000}s`);
}

// Get a value from the cache
export function getCacheValue<T>(key: string): T | null {
  if (!config.enabled) return null;
  
  performance.startMeasure(`cache-get-${key}`);
  
  const entry = cache.get(key) as CacheEntry<T> | undefined;
  const now = Date.now();
  
  // Check if the entry exists and is not expired
  if (entry && entry.expiresAt > now) {
    logger.debug(`Cache hit for key: ${key}`);
    performance.endMeasure(`cache-get-${key}`);
    return entry.data;
  }
  
  // If the entry is expired, remove it
  if (entry) {
    logger.debug(`Cache entry expired for key: ${key}`);
    cache.delete(key);
  } else {
    logger.debug(`Cache miss for key: ${key}`);
  }
  
  performance.endMeasure(`cache-get-${key}`);
  return null;
}

// Clear the entire cache or a specific key
export function clearCache(key?: string): void {
  if (key) {
    logger.debug(`Clearing cache for key: ${key}`);
    cache.delete(key);
  } else {
    logger.debug('Clearing entire cache');
    cache.clear();
  }
}

// Get cache statistics
export function getCacheStats(): {
  size: number;
  maxSize: number;
  enabled: boolean;
  entries: { key: string; expires: number; age: number }[];
} {
  const entries = Array.from(cache.entries()).map(([key, entry]) => ({
    key,
    expires: Math.max(0, Math.floor((entry.expiresAt - Date.now()) / 1000)),
    age: Math.floor((Date.now() - entry.timestamp) / 1000)
  }));
  
  return {
    size: cache.size,
    maxSize: config.maxSize,
    enabled: config.enabled,
    entries
  };
}

// Create a cached version of an API function
export function createCachedApiCall<T, Args extends any[]>(
  apiCall: (...args: Args) => Promise<T>,
  keyGenerator: (...args: Args) => string,
  ttl?: number
): (...args: Args) => Promise<T> {
  return async (...args: Args): Promise<T> => {
    const cacheKey = keyGenerator(...args);
    const cachedValue = getCacheValue<T>(cacheKey);
    
    if (cachedValue !== null) {
      return cachedValue;
    }
    
    performance.startMeasure(`api-call-${cacheKey}`);
    const result = await apiCall(...args);
    performance.endMeasure(`api-call-${cacheKey}`);
    
    setCacheValue(cacheKey, result, ttl);
    return result;
  };
}

export default {
  configureCache,
  generateCacheKey,
  setCacheValue,
  getCacheValue,
  clearCache,
  getCacheStats,
  createCachedApiCall
};
