import axios, { AxiosRequestConfig, AxiosResponse } from "axios"
import type dayjs from "dayjs"

import apiCache from "../utils/apiCache"
import errorHandler, {
  <PERSON><PERSON>r<PERSON><PERSON><PERSON><PERSON>,
  ErrorSeverity
} from "../utils/errorHandler"
import logger from "../utils/logger"
import performance from "../utils/performance"

// Create a logger instance for the API service
const apiLogger = logger.createLogger("ApiService")

// Get the auth token
const getToken = (): string | null => {
  try {
    const tokenWithQuotes = localStorage.getItem("SecureToken")
    return tokenWithQuotes?.replace(/"/g, "") || null // Remove quotes
  } catch (error) {
    apiLogger.error("Failed to get auth token", error)
    return null
  }
}

// Create an axios instance with default config
const api = axios.create({
  baseURL: "https://servicecenterapp.tesla.com/case/api",
  headers: {
    "Content-Type": "application/json"
  }
})

// Add request interceptor for performance monitoring and auth
api.interceptors.request.use(
  (config) => {
    // Add performance tracking
    const requestId = `${config.method}-${config.url}`
    performance.startMeasure(`api-request-${requestId}`)

    // Add auth token to every request
    const token = getToken()
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    apiLogger.debug(
      `API Request: ${config.method?.toUpperCase()} ${config.url}`,
      {
        params: config.params,
        data: config.data
      }
    )

    return config
  },
  (error) => {
    apiLogger.error("API Request Error", error)
    return Promise.reject(error)
  }
)

// Add response interceptor for performance monitoring and error handling
api.interceptors.response.use(
  (response) => {
    // End performance tracking
    const requestId = `${response.config.method}-${response.config.url}`
    performance.endMeasure(`api-request-${requestId}`)

    apiLogger.debug(`API Response: ${response.status} ${response.config.url}`, {
      data: response.data
    })

    return response
  },
  (error) => {
    // End performance tracking even for errors
    if (error.config) {
      const requestId = `${error.config.method}-${error.config.url}`
      performance.endMeasure(`api-request-${requestId}`)
    }

    // Handle API errors
    const errorInfo = errorHandler.createErrorInfo(
      `API Error: ${error.message}`,
      ErrorCategory.API,
      ErrorSeverity.HIGH,
      error,
      {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response?.status,
        data: error.response?.data
      }
    )

    errorHandler.handleError(errorInfo)
    return Promise.reject(error)
  }
)

/**
 * Set the Estimated Time of Completion (ETC) for a service visit
 * @param serviceVisitId The service visit ID
 * @param etcDate The estimated completion date
 * @returns Promise with the API response
 */
export const setETC = errorHandler.createApiErrorHandler(
  async (
    serviceVisitId: string,
    etcDate: dayjs.Dayjs
  ): Promise<AxiosResponse> => {
    performance.startMeasure(`setETC-${serviceVisitId}`)

    const formattedETCDate = etcDate.format("M/D/YYYY h:mm:ss A")

    const response = await api.put(
      `/visit/${serviceVisitId}/estimatedCompletionDateTime`,
      { Data: formattedETCDate, updateETP: true }
    )

    performance.endMeasure(`setETC-${serviceVisitId}`)
    return response
  },
  "Failed to set ETC"
)

/**
 * Set the key tag for a service visit
 * @param serviceVisitID The service visit ID
 * @param keyTag The key tag value
 * @returns Promise with the API response
 */
export const setKeyTag = errorHandler.createApiErrorHandler(
  async (serviceVisitID: string, keyTag: string): Promise<AxiosResponse> => {
    performance.startMeasure(`setKeyTag-${serviceVisitID}`)

    const response = await api.put("/visit/keytag", { serviceVisitID, keyTag })

    performance.endMeasure(`setKeyTag-${serviceVisitID}`)
    return response
  },
  "Failed to set key tag"
)

/**
 * Set the owner's transportation method for a service visit
 * @param serviceVisitId The service visit ID
 * @param keyTag The key tag value
 * @param etcDate The estimated completion date
 * @returns Promise with the API response
 */
export const setOwnersTransportationMethod = errorHandler.createApiErrorHandler(
  async (
    serviceVisitId: string,
    keyTag: string,
    etcDate: dayjs.Dayjs
  ): Promise<AxiosResponse | undefined> => {
    performance.startMeasure(`setOwnersTransportationMethod-${serviceVisitId}`)

    const transportationMethodID =
      keyTag === "prepared" ? 11 : keyTag === "preparedLoaner" ? 1 : null
    if (!transportationMethodID) {
      apiLogger.warn("Invalid keyTag value", { serviceVisitId, keyTag })
      performance.endMeasure(`setOwnersTransportationMethod-${serviceVisitId}`)
      return undefined
    }

    const formattedETCDate = etcDate.format("M/D/YYYY h:mm:ss A")

    const response = await api.put(
      `/visit/${serviceVisitId}/ownerstransportationmethod`,
      {
        transportationMethodID,
        loanerVIN: null, // Assuming null is always the case
        serviceVisitEndDateTime: formattedETCDate
      }
    )

    performance.endMeasure(`setOwnersTransportationMethod-${serviceVisitId}`)
    return response
  },
  "Failed to set owner's transportation method"
)

/**
 * Get service visit details (example of a cached API call)
 * @param serviceVisitId The service visit ID
 * @returns Promise with the service visit details
 */
export const getServiceVisitDetails = apiCache.createCachedApiCall(
  async (serviceVisitId: string): Promise<any> => {
    performance.startMeasure(`getServiceVisitDetails-${serviceVisitId}`)

    const response = await api.get(`/visit/${serviceVisitId}`)

    performance.endMeasure(`getServiceVisitDetails-${serviceVisitId}`)
    return response.data
  },
  (serviceVisitId: string) => `service-visit-${serviceVisitId}`,
  60 * 1000 // 1 minute cache TTL
)

// Export the API instance for direct use if needed
export default api
