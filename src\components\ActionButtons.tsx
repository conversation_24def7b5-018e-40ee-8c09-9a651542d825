import DirectionsCarIcon from "@mui/icons-material/DirectionsCar"
import EventAvailableIcon from "@mui/icons-material/EventAvailable"
import KeyIcon from "@mui/icons-material/Key"
import { Card, Divider, Stack, Typography } from "@mui/material"
import dayjs from "dayjs"
import React from "react"

// Import the actual components
import SetETC from "~src/components/SetETC"
import SetKeyTag from "~src/components/SetKeyTag"

// Import the API services directly
import {
  setETC,
  setKeyTag,
  setOwnersTransportationMethod
} from "../services/apiService"

interface ActionButtonsProps {
  etc: dayjs.Dayjs | null
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ etc }) => {
  return (
    <Card variant="outlined" sx={{ p: 1, mb: 1, bgcolor: "background.paper" }}>
      <Stack spacing={1}>
        <Typography
          variant="subtitle2"
          sx={{ fontWeight: 600, color: "primary.light", fontSize: "0.75rem" }}>
          Actions
        </Typography>

        <Divider />

        <Stack spacing={0.75}>
          {/* Set ETC Button - Using the actual SetETC component */}
          <SetETC etc={etc} />

          {/* Prepared Button - Using the actual SetKeyTag component */}
          <SetKeyTag keyTag="prepared" buttonName="Prepared" etcDate={etc} />

          {/* Prepared + Loaner Button - Using the actual SetKeyTag component */}
          <SetKeyTag
            keyTag="preparedLoaner"
            buttonName="Prepared + Loaner"
            etcDate={etc}
          />
        </Stack>
      </Stack>
    </Card>
  )
}

export default ActionButtons
