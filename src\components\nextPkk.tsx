// src/components/nextPkk.tsx
import RefreshIcon from "@mui/icons-material/Refresh"
import { Alert, Box, Button, CircularProgress, Typography } from "@mui/material"
import dayjs from "dayjs"
import React, { useEffect, useRef, useState } from "react"

/**
 * Validates a VIN (Vehicle Identification Number)
 * @param vin The VIN to validate
 * @returns True if the VIN is valid, false otherwise
 */
function isValidVIN(vin: string): boolean {
  if (!vin) return false

  // Basic VIN validation - should be 17 characters and alphanumeric
  if (vin.length !== 17) return false

  // Check if VIN contains only alphanumeric characters (excluding I, O, Q)
  const validVINPattern = /^[A-HJ-NPR-Z0-9]{17}$/i
  return validVINPattern.test(vin)
}

interface NextPkkProps {
  vin: string | null
}

const NextPkk: React.FC<NextPkkProps> = ({ vin }) => {
  const [vehicleData, setVehicleData] = useState(null)
  const [daysUntilKontrollfrist, setDaysUntilKontrollfrist] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [retryCount, setRetryCount] = useState(0)
  const [lastFetchedVin, setLastFetchedVin] = useState<string | null>(null)
  const fetchTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Function to fetch vehicle data with validation and delay
  const fetchVehicleData = (forceRefresh = false) => {
    // Clear any existing timeout
    if (fetchTimeoutRef.current) {
      clearTimeout(fetchTimeoutRef.current)
      fetchTimeoutRef.current = null
    }

    // Validate VIN
    if (!vin) {
      setError("No VIN provided. Cannot fetch vehicle data.")
      return
    }

    if (!isValidVIN(vin)) {
      setError(`Invalid VIN format: ${vin}`)
      return
    }

    // If we already fetched this VIN and it's not a forced refresh, don't fetch again
    if (vin === lastFetchedVin && !forceRefresh) {
      console.log(
        `Already fetched data for VIN: ${vin}, skipping duplicate fetch`
      )
      return
    }

    setLoading(true)
    setError("")
    console.log(
      `Preparing to fetch PKK data for VIN: ${vin} (forceRefresh: ${forceRefresh})`
    )

    // Set a timeout to ensure DOM is fully loaded and VIN is stable
    fetchTimeoutRef.current = setTimeout(() => {
      console.log(`Executing fetch for VIN: ${vin} after delay`)

      chrome.runtime.sendMessage(
        {
          type: "GET_VEGVESEN_INFO",
          vin,
          forceRefresh,
          retryCount: 4,
          retryDelay: 1500
        },
        (response) => {
          setLoading(false)
          setLastFetchedVin(vin)

          if (response && response.error) {
            console.error(
              `Error fetching vehicle data for VIN ${vin}:`,
              response.error
            )
            setError(`Failed to fetch vehicle data: ${response.error}`)
            setVehicleData(null)
          } else if (!response || !response.data) {
            console.error(`No response or data received for VIN ${vin}`)
            setError("No response received from the server.")
            setVehicleData(null)
          } else {
            console.log(`Vehicle data received for VIN ${vin}:`, response.data)
            setVehicleData(response.data)

            if (
              response.data.kjoretoydataListe &&
              response.data.kjoretoydataListe[0]?.periodiskKjoretoyKontroll
                ?.kontrollfrist
            ) {
              const kontrollfrist = dayjs(
                response.data.kjoretoydataListe[0].periodiskKjoretoyKontroll
                  .kontrollfrist
              )
              const today = dayjs()
              const daysDiff = kontrollfrist.diff(today, "day")
              setDaysUntilKontrollfrist(daysDiff)
              console.log(`Days until kontrollfrist: ${daysDiff}`)
            } else {
              console.warn(
                `No kontrollfrist data found in the response for VIN ${vin}`
              )
              setDaysUntilKontrollfrist(null)
            }
          }
        }
      )
    }, 1000) // 1 second delay to ensure VIN is stable
  }

  // Initial fetch on component mount or when VIN changes
  useEffect(() => {
    if (vin && vin !== lastFetchedVin) {
      console.log(
        `VIN changed from ${lastFetchedVin} to ${vin}, fetching new data`
      )
      fetchVehicleData(false)
    }
  }, [vin, retryCount])

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (fetchTimeoutRef.current) {
        clearTimeout(fetchTimeoutRef.current)
      }
    }
  }, [])

  const handleRetry = () => {
    console.log(`Manual retry requested for VIN: ${vin}`)
    setRetryCount((prevCount) => prevCount + 1)
    fetchVehicleData(true) // Force refresh on manual retry
  }

  // Display loading state
  if (loading) {
    return (
      <Box
        display="flex"
        alignItems="center"
        justifyContent="center"
        p={1.5}
        sx={{
          borderRadius: 2,
          backgroundColor: "background.paper",
          minHeight: 100
        }}>
        <CircularProgress size={20} sx={{ mr: 1.5, color: "primary.light" }} />
        <Typography variant="body2">Loading PKK data...</Typography>
      </Box>
    )
  }

  // Display error state
  if (error) {
    return (
      <Box
        sx={{
          p: 1.5,
          borderRadius: 2,
          backgroundColor: "background.paper"
        }}>
        <Alert
          severity="error"
          variant="filled"
          sx={{
            mb: 1.5,
            borderRadius: 1.5,
            "& .MuiAlert-icon": {
              fontSize: "1.25rem"
            }
          }}>
          {error}
        </Alert>
        <Button
          variant="contained"
          color="primary"
          startIcon={<RefreshIcon />}
          onClick={handleRetry}
          size="small"
          fullWidth
          sx={{
            mb: 1,
            fontWeight: 500,
            boxShadow: 2
          }}>
          Retry
        </Button>
        {vin && (
          <Typography
            variant="caption"
            display="block"
            align="center"
            sx={{ color: "text.secondary" }}>
            VIN: {vin.slice(-6)}
          </Typography>
        )}
      </Box>
    )
  }

  // Display no data state
  if (
    !vehicleData ||
    !vehicleData.kjoretoydataListe ||
    vehicleData.kjoretoydataListe.length === 0
  ) {
    return (
      <Box
        sx={{
          p: 1.5,
          borderRadius: 2,
          backgroundColor: "background.paper"
        }}>
        <Alert
          severity="warning"
          variant="filled"
          sx={{
            mb: 1.5,
            borderRadius: 1.5,
            "& .MuiAlert-icon": {
              fontSize: "1.25rem"
            }
          }}>
          No PKK data available for this vehicle.
        </Alert>
        <Button
          variant="contained"
          color="primary"
          startIcon={<RefreshIcon />}
          onClick={handleRetry}
          size="small"
          fullWidth
          sx={{
            mb: 1,
            fontWeight: 500,
            boxShadow: 2
          }}>
          Retry
        </Button>
        {vin && (
          <Typography
            variant="caption"
            display="block"
            align="center"
            sx={{ color: "text.secondary" }}>
            VIN: {vin.slice(-6)}
          </Typography>
        )}
      </Box>
    )
  }

  // Extract kontrollfrist data
  const kontrollfrist =
    vehicleData.kjoretoydataListe[0]?.periodiskKjoretoyKontroll?.kontrollfrist

  // Determine status color and icon based on days until kontrollfrist
  let statusColor = "inherit"
  let statusText = ""
  let statusBgColor = "transparent"

  if (daysUntilKontrollfrist !== null) {
    if (daysUntilKontrollfrist < 0) {
      statusColor = "error.main"
      statusText = "Overdue!"
      statusBgColor = "error.dark"
    } else if (daysUntilKontrollfrist < 30) {
      statusColor = "error.main"
      statusText = "Soon!"
      statusBgColor = "error.dark"
    } else if (daysUntilKontrollfrist < 90) {
      statusColor = "warning.main"
      statusText = "Coming up"
      statusBgColor = "warning.dark"
    } else {
      statusColor = "success.main"
      statusText = "OK"
      statusBgColor = "success.dark"
    }
  }

  // Display data state
  return (
    <Box
      sx={{
        p: 1.5,
        borderRadius: 2,
        backgroundColor: "background.paper"
      }}>
      {/* PKK Date */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 1
        }}>
        <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
          Next PKK:
        </Typography>
        <Typography
          variant="subtitle2"
          sx={{
            fontWeight: 600,
            color: kontrollfrist ? "primary.light" : "text.secondary"
          }}>
          {kontrollfrist
            ? dayjs(kontrollfrist).format("DD.MM.YYYY")
            : "No data"}
        </Typography>
      </Box>

      {/* Days until PKK with status indicator */}
      {daysUntilKontrollfrist !== null && (
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 1.5
          }}>
          <Typography variant="subtitle2" sx={{ fontWeight: 500 }}>
            Days remaining:
          </Typography>
          <Box
            sx={{
              display: "flex",
              alignItems: "center"
            }}>
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 600,
                color: statusColor,
                mr: 0.75
              }}>
              {daysUntilKontrollfrist}
            </Typography>
            <Box
              sx={{
                backgroundColor: statusBgColor,
                color: "#fff",
                fontSize: "0.7rem",
                fontWeight: 600,
                px: 0.75,
                py: 0.25,
                borderRadius: 1,
                display: "inline-block"
              }}>
              {statusText}
            </Box>
          </Box>
        </Box>
      )}

      {/* Refresh button */}
      <Button
        variant="outlined"
        color="primary"
        startIcon={<RefreshIcon />}
        onClick={handleRetry}
        size="small"
        fullWidth
        sx={{
          mb: 1,
          fontWeight: 500
        }}>
        Refresh
      </Button>

      {/* VIN display */}
      {vin && (
        <Typography
          variant="caption"
          display="block"
          align="center"
          sx={{ color: "text.secondary" }}>
          VIN: {vin.slice(-6)}
        </Typography>
      )}
    </Box>
  )
}

export default NextPkk
