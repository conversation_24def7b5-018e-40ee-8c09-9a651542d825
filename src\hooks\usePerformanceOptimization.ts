/**
 * React hooks for performance optimization
 */
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import performance from '../utils/performance';

/**
 * Hook to memoize expensive calculations with performance tracking
 * @param calculationFn Function to perform the calculation
 * @param dependencies Dependencies array for the calculation
 * @param calculationName Optional name for performance tracking
 * @returns The memoized calculation result
 */
export function useMemoWithPerformance<T>(
  calculationFn: () => T,
  dependencies: any[],
  calculationName?: string
): T {
  const name = calculationName || 'anonymous-calculation';
  
  return useMemo(() => {
    performance.startMeasure(name);
    const result = calculationFn();
    performance.endMeasure(name);
    return result;
  }, dependencies);
}

/**
 * Hook to debounce a value change
 * @param value The value to debounce
 * @param delay The debounce delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

/**
 * Hook to throttle a function
 * @param fn The function to throttle
 * @param limit The throttle limit in milliseconds
 * @returns The throttled function
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number
): T {
  const lastRun = useRef<number>(0);
  const lastFunc = useRef<T>(fn);
  
  useEffect(() => {
    lastFunc.current = fn;
  }, [fn]);
  
  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastRun.current >= limit) {
      lastRun.current = now;
      return lastFunc.current(...args);
    }
  }, [limit]) as T;
}

/**
 * Hook to detect when a component is visible in the viewport
 * @param options IntersectionObserver options
 * @returns [ref, isVisible] - Ref to attach to the element and boolean indicating visibility
 */
export function useIntersectionObserver(
  options: IntersectionObserverInit = { threshold: 0 }
): [React.RefObject<HTMLElement>, boolean] {
  const ref = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState<boolean>(false);
  
  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      setIsVisible(entry.isIntersecting);
    }, options);
    
    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }
    
    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [options]);
  
  return [ref, isVisible];
}

/**
 * Hook to measure component render time
 * @param componentName Name of the component for tracking
 */
export function useRenderPerformance(componentName: string): void {
  const renderCount = useRef(0);
  
  useEffect(() => {
    renderCount.current += 1;
    performance.startMeasure(`${componentName}-render-${renderCount.current}`);
    
    return () => {
      performance.endMeasure(`${componentName}-render-${renderCount.current}`);
    };
  });
}

export default {
  useMemoWithPerformance,
  useDebounce,
  useThrottle,
  useIntersectionObserver,
  useRenderPerformance
};
