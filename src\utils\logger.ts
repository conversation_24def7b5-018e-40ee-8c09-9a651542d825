/**
 * Logger utility for consistent logging across the application
 * Provides different log levels and formatting
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

// Default configuration
const DEFAULT_CONFIG = {
  level: LogLevel.INFO,
  enableTimestamp: true,
  enableConsole: true,
  prefix: 'Tesla Service Assistant'
};

// Current configuration
let config = { ...DEFAULT_CONFIG };

/**
 * Configure the logger
 * @param options Configuration options
 */
export function configureLogger(options: Partial<typeof DEFAULT_CONFIG>) {
  config = { ...config, ...options };
}

/**
 * Format a log message with timestamp and prefix
 * @param level The log level
 * @param message The message to log
 * @param data Additional data to log
 * @returns Formatted log message
 */
function formatLogMessage(level: LogLevel, message: string, data?: any): string {
  const parts = [];
  
  if (config.enableTimestamp) {
    parts.push(`[${new Date().toISOString()}]`);
  }
  
  parts.push(`[${config.prefix}]`);
  parts.push(`[${LogLevel[level]}]`);
  parts.push(message);
  
  return parts.join(' ');
}

/**
 * Log a message at the specified level
 * @param level The log level
 * @param message The message to log
 * @param data Additional data to log
 */
export function log(level: LogLevel, message: string, data?: any) {
  if (level < config.level) return;
  
  const formattedMessage = formatLogMessage(level, message, data);
  
  if (config.enableConsole) {
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, data !== undefined ? data : '');
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, data !== undefined ? data : '');
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, data !== undefined ? data : '');
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage, data !== undefined ? data : '');
        break;
    }
  }
}

/**
 * Log a debug message
 * @param message The message to log
 * @param data Additional data to log
 */
export function debug(message: string, data?: any) {
  log(LogLevel.DEBUG, message, data);
}

/**
 * Log an info message
 * @param message The message to log
 * @param data Additional data to log
 */
export function info(message: string, data?: any) {
  log(LogLevel.INFO, message, data);
}

/**
 * Log a warning message
 * @param message The message to log
 * @param data Additional data to log
 */
export function warn(message: string, data?: any) {
  log(LogLevel.WARN, message, data);
}

/**
 * Log an error message
 * @param message The message to log
 * @param data Additional data to log
 */
export function error(message: string, data?: any) {
  log(LogLevel.ERROR, message, data);
}

/**
 * Create a logger instance with a specific context
 * @param context The context for this logger
 * @returns Logger functions for the specific context
 */
export function createLogger(context: string) {
  const contextPrefix = `[${context}]`;
  
  return {
    debug: (message: string, data?: any) => debug(`${contextPrefix} ${message}`, data),
    info: (message: string, data?: any) => info(`${contextPrefix} ${message}`, data),
    warn: (message: string, data?: any) => warn(`${contextPrefix} ${message}`, data),
    error: (message: string, data?: any) => error(`${contextPrefix} ${message}`, data)
  };
}

export default {
  configure: configureLogger,
  debug,
  info,
  warn,
  error,
  createLogger
};
