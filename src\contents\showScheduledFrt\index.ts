import type { PlasmoCSConfig } from "plasmo"

export const config: PlasmoCSConfig = {
  matches: [
    "https://serviceapp.tesla.com/service/service-home/product-details/*"
  ],
  all_frames: true
}

const checkAndClickFrt = (): boolean => {
  const elementSelector =
    "#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div.width-290 > div.width-100.sa-padding.ng-star-inserted > div > div:nth-child(2) > div"
  const clickSelector =
    "#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div.width-290 > div.width-100.sa-padding.ng-star-inserted > div > div:nth-child(2) > span"

  const element = document.querySelector(elementSelector)
  if (!element) {
    const clickElement = document.querySelector(clickSelector) as HTMLElement
    if (clickElement) {
      clickElement.click()
      return true // Element was not present and click was simulated.
    }
  }
  return false // Element is present, no action taken.
}

const checkAndClickInfo = (): boolean => {
  const elementSelector =
    "#assetDetails > div > div > div > div > div > div:nth-child(3) > mat-list > mat-list-item:nth-child(1) > span > div.tcc-asset-property-name"
  const clickSelector =
    "#container > div.ng-star-inserted > vehicle-asset-detail > div.asset.ng-star-inserted > div.asset-expand > button > span.mat-button-wrapper > mat-icon"

  const element = document.querySelector(elementSelector)
  if (!element) {
    const clickElement = document.querySelector(clickSelector) as HTMLElement
    if (clickElement) {
      clickElement.click()
      return true // Element was not present and click was simulated.
    }
  }
  return false // Element is present, no action taken.
}

// Use MutationObserver to watch for changes in the DOM
const observer = new MutationObserver(() => {
  const wasClickedFrt = checkAndClickFrt()
  const wasClickedInfo = checkAndClickInfo()

  // Check if both elements were not clicked (i.e., are visible or not needed to be clicked)
  // You might want to adjust the logic based on what should happen when neither or either is clicked
  if (wasClickedFrt || wasClickedInfo) {
    // If you only want to disconnect after both specific interactions have been made,
    // you could set flags to keep track of each interaction and only disconnect when
    // both flags are true (indicating both actions have been taken).
    // Example:
    // if (wasClickedFrt && wasClickedInfo) observer.disconnect();
    // For this example, the observer does not disconnect automatically,
    // allowing it to keep checking and acting on changes.
  }
})

// Start observing the entire document
observer.observe(document, { childList: true, subtree: true })
