import createCache from "@emotion/cache"
import { CacheProvider } from "@emotion/react"
import { Close, Menu } from "@mui/icons-material"
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth"
import InsertInvitationIcon from "@mui/icons-material/InsertInvitation"
import ScheduleIcon from "@mui/icons-material/Schedule"
import {
  Box,
  Card,
  CardContent,
  Divider,
  IconButton,
  Stack,
  Tooltip,
  Typography
} from "@mui/material"
import { ThemeProvider } from "@mui/material/styles"
import dayjs from "dayjs"
// Types
import type { PlasmoCSConfig } from "plasmo"
import React, { useEffect, useRef, useState } from "react"
import Draggable from "react-draggable"

// Local Components and Utils
import ActionButtons from "../components/ActionButtons"
import AdjustETC from "../components/AdjustETC"
import NextPkk from "../components/nextPkk"
import useFetchDataOnElementPresence from "../hooks/useETC"
import theme from "../theme"
import { formatDuration } from "../utils/formatDuration"

// Configurations and Constants
dayjs.locale("nb")

const styleElement = document.createElement("style")
const styleCache = createCache({
  key: "plasmo-mui-cache",
  prepend: true,
  container: styleElement
})

export const config: PlasmoCSConfig = {
  matches: [
    "https://serviceapp.tesla.com/service/service-home/product-details/*"
  ]
}

export const getStyle = () => styleElement

function EtcOverlay() {
  const {
    vin,
    appointmentDate,
    etc: fetchedEtc,
    duration: fetchedDuration
  } = useFetchDataOnElementPresence()

  const [etc, setEtc] = useState(fetchedEtc)
  const [duration, setDuration] = useState(fetchedDuration)
  const [isVisible, setIsVisible] = useState(true) // state to manage sidebar visibility
  const originalEtcRef = useRef<dayjs.Dayjs | null>(null)

  useEffect(() => {
    setEtc(fetchedEtc)
    setDuration(fetchedDuration)

    // Store the original ETC for reset functionality
    if (fetchedEtc && !originalEtcRef.current) {
      originalEtcRef.current = fetchedEtc
    }
  }, [fetchedEtc, fetchedDuration])

  const toggleVisibility = () => {
    setIsVisible(!isVisible)
  }

  return (
    <CacheProvider value={styleCache}>
      <ThemeProvider theme={theme}>
        <Draggable handle=".handle">
          <Stack
            sx={{
              maxWidth: isVisible ? 280 : 40, // Toggle width
              position: "fixed",
              top: 200, // 200px from the top
              right: 20, // 20px from the right edge
              flexDirection: "column",
              zIndex: 9999
            }}>
            <Card
              elevation={8}
              sx={{
                overflow: "hidden",
                border: "1px solid rgba(255, 255, 255, 0.12)",
                transition: "all 0.3s ease"
              }}>
              {/* Header with drag handle */}
              <Box
                className="handle"
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                  backgroundColor: "background.paper",
                  py: 0.5,
                  px: 1,
                  cursor: "move",
                  borderBottom: "1px solid rgba(255, 255, 255, 0.12)"
                }}>
                <Typography
                  variant="subtitle2"
                  sx={{
                    fontWeight: 600,
                    color: "primary.light"
                  }}>
                  {isVisible ? "Tesla Service Assistant" : ""}
                </Typography>
                <Tooltip title={isVisible ? "Collapse" : "Expand"}>
                  <IconButton
                    size="small"
                    onClick={toggleVisibility}
                    sx={{
                      cursor: "pointer",
                      color: "text.secondary",
                      "&:hover": {
                        color: "primary.light"
                      }
                    }}>
                    {isVisible ? (
                      <Close fontSize="small" />
                    ) : (
                      <Menu fontSize="small" />
                    )}
                  </IconButton>
                </Tooltip>
              </Box>

              {isVisible && (
                <>
                  <CardContent sx={{ p: 1.5, pt: 1.5 }}>
                    {/* Appointment Info Section */}
                    <Box sx={{ mb: 1.5 }}>
                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          mb: 1
                        }}>
                        <CalendarMonthIcon
                          fontSize="small"
                          sx={{
                            mr: 1,
                            color: "primary.light"
                          }}
                        />
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 500,
                            color: appointmentDate
                              ? "text.primary"
                              : "text.secondary"
                          }}>
                          {appointmentDate
                            ? appointmentDate.format("ddd D. MMM [kl.] HH:mm")
                            : "Loading appointment..."}
                        </Typography>
                      </Box>

                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center",
                          mb: 1
                        }}>
                        <ScheduleIcon
                          fontSize="small"
                          sx={{
                            mr: 1,
                            color: "primary.light"
                          }}
                        />
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 500,
                            color:
                              duration !== null
                                ? "text.primary"
                                : "text.secondary"
                          }}>
                          {duration !== null
                            ? formatDuration(duration)
                            : "Calculating duration..."}
                        </Typography>
                      </Box>

                      <Box
                        sx={{
                          display: "flex",
                          alignItems: "center"
                        }}>
                        <InsertInvitationIcon
                          fontSize="small"
                          sx={{
                            mr: 1,
                            color: "primary.light"
                          }}
                        />
                        <Typography
                          variant="body2"
                          sx={{
                            fontWeight: 500,
                            color: etc ? "text.primary" : "text.secondary"
                          }}>
                          {etc
                            ? etc.format("ddd D. MMM [kl.] HH:mm")
                            : "Calculating ETC..."}
                        </Typography>
                      </Box>
                    </Box>

                    {/* Divider */}
                    <Divider sx={{ my: 1.5 }} />

                    {/* PKK Section */}
                    <Box>
                      <NextPkk vin={vin} />
                    </Box>

                    {/* Adjust ETC Section */}
                    <Box sx={{ mt: 2 }}>
                      <AdjustETC
                        etc={etc}
                        originalEtc={originalEtcRef.current}
                        duration={duration}
                        setEtc={setEtc}
                        setDuration={setDuration}
                      />
                    </Box>

                    {/* Actions Section */}
                    <Box sx={{ mt: 1 }}>
                      <ActionButtons etc={etc} />
                    </Box>
                  </CardContent>
                </>
              )}
            </Card>
          </Stack>
        </Draggable>
      </ThemeProvider>
    </CacheProvider>
  )
}

export default EtcOverlay
