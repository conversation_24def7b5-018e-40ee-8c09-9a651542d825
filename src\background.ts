import dayjs from "dayjs"
import timezone from "dayjs/plugin/timezone"
import utc from "dayjs/plugin/utc"

import { Storage } from "@plasmohq/storage"

// Initialize dayjs with plugins
dayjs.extend(timezone)
dayjs.extend(utc)

// Setting default values
const defaultWOpeningHours = dayjs()
  .set("hour", 7)
  .set("minute", 30)
  .toISOString() // Convert to ISO string
const defaultWClosingHours = dayjs()
  .set("hour", 19)
  .set("minute", 0)
  .toISOString() // Convert to ISO string
const defaulfohOpeningHours = dayjs()
  .set("hour", 7)
  .set("minute", 0)
  .toISOString() // Convert to ISO string
const defaulfohClosingHours = dayjs()
  .set("hour", 19)
  .set("minute", 0)
  .toISOString() // Convert to ISO string

async function options() {
  const storage = new Storage({
    copiedKeyList: ["shield-modulation"]
  })

  console.log("Initializing default values...")

  const initialValues = {
    wOpeningHours: defaultWOpeningHours,
    wClosingHours: defaultWClosingHours,
    minimumHours: 3.99,
    multiplier: 2,
    fohOpeningHours: defaulfohOpeningHours,
    fohClosingHours: defaulfohClosingHours
  }

  for (const [key, defaultValue] of Object.entries(initialValues)) {
    const value = await storage.get(key)
    if (value === undefined) {
      // Check if the value is not already set
      console.log(`Setting default for ${key}: ${defaultValue}`)
      await storage.set(key, defaultValue)
    } else {
      console.log(`Value already set for ${key}: Skipping`)
    }
  }
  console.log("Initialization complete.")
}

// Ensure this is called at the right time in your extension's lifecycle
options()
  .then(() => {
    console.log("Default options have been initialized.")
  })
  .catch((error) => {
    console.error("Error initializing options:", error)
  })

// Cache for storing Vegvesen API responses
const vegvesenCache = new Map<string, { data: any; timestamp: number }>()

/**
 * Validates a VIN (Vehicle Identification Number)
 * @param vin The VIN to validate
 * @returns True if the VIN is valid, false otherwise
 */
function isValidVIN(vin: string): boolean {
  if (!vin) {
    console.error("VIN is empty or undefined")
    return false
  }

  // Basic VIN validation - should be 17 characters and alphanumeric
  if (vin.length !== 17) {
    console.error(`Invalid VIN length: ${vin.length}, expected 17 characters`)
    return false
  }

  // Check if VIN contains only alphanumeric characters (excluding I, O, Q)
  const validVINPattern = /^[A-HJ-NPR-Z0-9]{17}$/i
  if (!validVINPattern.test(vin)) {
    console.error(`VIN contains invalid characters: ${vin}`)
    return false
  }

  console.log(`VIN validation passed for: ${vin}`)
  return true
}

/**
 * Fetches vehicle data from Vegvesen API with retry mechanism and caching
 * @param vin The vehicle identification number
 * @param retryCount Number of retry attempts
 * @param retryDelay Delay between retries in milliseconds
 * @param forceRefresh Whether to force a refresh from the API instead of using cache
 * @returns The vehicle data or an error object
 */
async function fetchVegvesenData(
  vin: string,
  retryCount = 3,
  retryDelay = 1000,
  forceRefresh = false
) {
  console.log(`fetchVegvesenData called with VIN: ${vin}`)

  // Validate VIN format
  if (!isValidVIN(vin)) {
    return { error: `Invalid VIN format: ${vin}` }
  }

  // Check cache first (if not forcing refresh)
  if (!forceRefresh) {
    const cachedData = vegvesenCache.get(vin)
    if (cachedData) {
      const cacheAge = Date.now() - cachedData.timestamp
      // Use cache if it's less than 24 hours old
      if (cacheAge < 24 * 60 * 60 * 1000) {
        console.log(
          `Using cached Vegvesen data for VIN: ${vin}, cache age: ${Math.round(
            cacheAge / 1000 / 60
          )} minutes`
        )
        return cachedData.data
      } else {
        console.log(`Cache expired for VIN: ${vin}, fetching fresh data`)
      }
    }
  }

  // Proceed with API call
  for (let attempt = 1; attempt <= retryCount; attempt++) {
    try {
      console.log(`Attempt ${attempt} to fetch Vegvesen data for VIN: ${vin}`)

      // Add a small delay before the first attempt to ensure any DOM updates are complete
      if (attempt === 1) {
        await new Promise((resolve) => setTimeout(resolve, 500))
      }

      const apiUrl = `https://www.vegvesen.no/ws/no/vegvesen/kjoretoy/felles/datautlevering/enkeltoppslag/kjoretoydata?understellsnummer=${encodeURIComponent(
        vin
      )}`
      console.log(`Calling Vegvesen API: ${apiUrl}`)

      const response = await fetch(apiUrl, {
        method: "GET",
        headers: {
          "SVV-Authorization": "4d08ccef-b32b-40ed-acb7-16fd7274d6b1",
          Accept: "application/json",
          "Cache-Control": "no-cache"
        }
      })

      // Log the response status
      console.log(`Vegvesen API response status: ${response.status}`)

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(
          `HTTP error! status: ${response.status}, message: ${errorText}`
        )
      }

      const data = await response.json()
      console.log(`Received data from Vegvesen API:`, data)

      // Validate the response data
      if (
        !data ||
        !data.kjoretoydataListe ||
        data.kjoretoydataListe.length === 0
      ) {
        throw new Error("Invalid or empty response data structure")
      }

      // Store in cache
      vegvesenCache.set(vin, {
        data: data,
        timestamp: Date.now()
      })

      console.log("Successfully fetched and cached Vegvesen data")
      return data
    } catch (error) {
      console.error(`Attempt ${attempt} failed:`, error)

      // If we've reached the maximum number of retries, return the error details
      if (attempt === retryCount) {
        const errorResult = {
          error: error.message || "Failed to fetch vehicle data"
        }
        return errorResult
      }

      // Wait before the next retry with exponential backoff
      const backoffDelay = retryDelay * Math.pow(2, attempt - 1)
      console.log(`Waiting ${backoffDelay}ms before retry ${attempt + 1}`)
      await new Promise((resolve) => setTimeout(resolve, backoffDelay))
    }
  }
}

// Listening for messages from the content script or popup script
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.type === "GET_VEGVESEN_INFO") {
    console.log(`Received GET_VEGVESEN_INFO request for VIN: ${request.vin}`)

    // Check if VIN is provided
    if (!request.vin) {
      console.error("No VIN provided in the request")
      sendResponse({ error: "No VIN provided" })
      return true
    }

    // Extract options from request
    const forceRefresh = request.forceRefresh || false
    const retryCount = request.retryCount || 3
    const retryDelay = request.retryDelay || 1000

    console.log(
      `Processing request with options: forceRefresh=${forceRefresh}, retryCount=${retryCount}, retryDelay=${retryDelay}`
    )

    // Call the API with a small delay to ensure DOM is fully loaded
    setTimeout(() => {
      fetchVegvesenData(request.vin, retryCount, retryDelay, forceRefresh)
        .then((result) => {
          if (result && result.error) {
            // If we got an error object from fetchVegvesenData
            console.error("Error from fetchVegvesenData:", result.error)
            sendResponse({ error: result.error })
          } else {
            // If we got valid data
            console.log("Successfully retrieved vehicle data")
            sendResponse({ data: result })
          }
        })
        .catch((error) => {
          // This should only happen if there's an unhandled exception in fetchVegvesenData
          console.error("Unhandled exception in fetchVegvesenData:", error)
          sendResponse({ error: error.message || "Failed to fetch data" })
        })
    }, 300) // Small delay to ensure DOM is fully loaded

    return true // indicates that you wish to send a response asynchronously
  }
})

// Execute the options function to set default values
options()
