{"name": "sca", "displayName": "Sca", "version": "0.0.1", "description": "A basic Plasmo extension.", "author": "JN", "scripts": {"dev": "plasmo dev", "build": "plasmo build", "test": "plasmo test"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.12", "@mui/lab": "5.0.0-alpha.146", "@mui/material": "^5.14.10", "@mui/x-date-pickers": "^6.15.0", "@plasmohq/storage": "^1.8.0", "axios": "^1.5.1", "dayjs": "^1.11.10", "dayjs-plugin-utc": "^0.1.2", "plasmo": "0.83.0", "react": "18.2.0", "react-dom": "18.2.0", "react-draggable": "^4.4.6"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "4.1.0", "@types/axios": "^0.14.0", "@types/chrome": "0.0.245", "@types/node": "20.5.9", "@types/react": "18.2.21", "@types/react-dom": "18.2.7", "assert": "^2.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "^1.0.0", "prettier": "3.0.3", "process": "^0.11.10", "stream-browserify": "^3.0.0", "stream-http": "^3.1.0", "typescript": "5.2.2", "util": "^0.12.3"}, "manifest": {"host_permissions": ["https://*/*"]}}