import CheckCircleIcon from "@mui/icons-material/CheckCircle"
import SaveIcon from "@mui/icons-material/Save"
import { Box, Tooltip } from "@mui/material"
import <PERSON>ton from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import dayjs from "dayjs"
import advancedFormat from "dayjs/plugin/advancedFormat"
import React, { useState } from "react"

import { setETC } from "../services/apiService"
import logger from "../utils/logger"

// Create a logger for this component
const log = logger.createLogger("SetETC")

dayjs.extend(advancedFormat)

interface SetETCProps {
  etc: dayjs.Dayjs | null
}

const SetETC: React.FC<SetETCProps> = ({ etc }) => {
  const [apiSuccess, setApiSuccess] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const serviceVisitId = window.location.pathname.split("/").pop()

  /**
   * Extracts the original ETC from a value that might contain "(was: ...)" format
   * @param value The input value that might contain previous ETC reference
   * @returns The clean ETC value without the "(was: ...)" part
   */
  const extractOriginalETC = (value: string): string => {
    if (!value) return "Not set"

    // If the value contains "(was: ...)", extract just the original ETC
    if (value.includes("(was: ")) {
      return value.split(" (was: ")[0]
    }

    return value
  }

  /**
   * Creates a formatted display value showing both new and previous ETC
   * @param newETC The new ETC formatted string
   * @param previousETC The previous ETC string
   * @param newETCDate The new ETC as a dayjs object for calculations
   * @returns Formatted display string
   */
  const createETCDisplayValue = (
    newETC: string,
    previousETC: string,
    newETCDate: dayjs.Dayjs
  ): string => {
    if (previousETC === "Not set") {
      return `${newETC} (was: ${previousETC})`
    }

    try {
      // Try to parse the previous ETC to calculate time difference
      // Handle different possible formats
      let previousETCDate: dayjs.Dayjs | null = null

      // Try parsing with the expected format first
      if (previousETC.includes("-") && previousETC.includes(":")) {
        previousETCDate = dayjs(previousETC, "ddd DD-MMM-YYYY - hh:mm A")

        // If that doesn't work, try other common formats
        if (!previousETCDate.isValid()) {
          previousETCDate = dayjs(previousETC)
        }
      }

      if (previousETCDate && previousETCDate.isValid()) {
        const diffMinutes = newETCDate.diff(previousETCDate, "minutes")

        let timeDiffText = ""
        if (Math.abs(diffMinutes) < 60) {
          timeDiffText = `${Math.abs(diffMinutes)}m ${
            diffMinutes >= 0 ? "later" : "earlier"
          }`
        } else {
          const hours = Math.floor(Math.abs(diffMinutes) / 60)
          const minutes = Math.abs(diffMinutes) % 60
          timeDiffText = `${hours}h${minutes > 0 ? ` ${minutes}m` : ""} ${
            diffMinutes >= 0 ? "later" : "earlier"
          }`
        }

        return `${newETC} (was: ${previousETC}, ${timeDiffText})`
      }
    } catch (error) {
      log.debug("Could not calculate time difference", error)
    }

    // Fallback to simple format if time calculation fails
    return `${newETC} (was: ${previousETC})`
  }

  /**
   * Updates the ETC field in the Tesla UI and adds a check mark
   * @param etcDate The new ETC date to display
   */
  const updateTeslaUIField = (etcDate: dayjs.Dayjs) => {
    try {
      // Format the new ETC date in the required format: "Tue 13-May-2025 - 10:00 AM"
      const formattedNewETC = etcDate
        .locale("en")
        .format("ddd DD-MMM-YYYY - hh:mm A")

      // Try different possible input element IDs since they might change
      const possibleInputIds = [
        "mat-input-3",
        "mat-input-8",
        "mat-input-0",
        "mat-input-7"
      ]

      // Try to find the input element
      let inputElem = null
      for (const id of possibleInputIds) {
        const elem = document.getElementById(id)
        if (elem) {
          inputElem = elem
          break
        }
      }

      // If we found an input element, update its value
      if (inputElem) {
        const inputElement = inputElem as HTMLInputElement

        // Get the previous ETC value for reference
        const previousETC = extractOriginalETC(inputElement.value)

        // Create a display value that shows both previous and new ETC with time difference
        const displayValue = createETCDisplayValue(
          formattedNewETC,
          previousETC,
          etcDate
        )

        inputElement.value = displayValue
        log.debug(
          `Updated Tesla UI field with ETC: ${formattedNewETC}, Previous: ${previousETC}`
        )

        // Add check mark after the calendar icon
        addCheckMarkToCalendar()
      } else {
        // If we couldn't find the input by ID, try using a more general selector
        const inputs = document.querySelectorAll("input.mat-input-element")
        if (inputs.length > 0) {
          // Look for an input that might contain a date format
          for (let i = 0; i < inputs.length; i++) {
            const input = inputs[i] as HTMLInputElement
            if (
              input.value &&
              (input.value.includes("-") || input.value.includes("/"))
            ) {
              // Get the previous ETC value for reference
              const previousETC = extractOriginalETC(input.value)

              // Create a display value that shows both previous and new ETC
              const displayValue = `${formattedNewETC} (was: ${previousETC})`

              input.value = displayValue
              log.debug(
                `Updated Tesla UI field with ETC: ${formattedNewETC}, Previous: ${previousETC} using general selector`
              )

              // Add check mark after the calendar icon
              addCheckMarkToCalendar()
              break
            }
          }
        } else {
          log.warn("Could not find ETC input field in Tesla UI")
        }
      }
    } catch (error) {
      log.error("Error updating Tesla UI field", error)
    }
  }

  /**
   * Adds a check mark icon after the calendar icon
   */
  const addCheckMarkToCalendar = () => {
    try {
      // First, remove any existing check mark to avoid duplicates
      const existingCheckMark = document.getElementById("etc-check-mark")
      if (existingCheckMark) {
        existingCheckMark.remove()
      }

      // Find the calendar icon
      const calendarIcon = document.querySelector('img[src*="calendar.svg"]')
      if (calendarIcon) {
        // Create the check mark element
        const checkMark = document.createElement("span")
        checkMark.id = "etc-check-mark"
        checkMark.innerHTML = "✓"
        checkMark.style.color = "#4CAF50" // Green color
        checkMark.style.fontWeight = "bold"
        checkMark.style.fontSize = "16px"
        checkMark.style.marginLeft = "5px"
        checkMark.style.position = "relative"
        checkMark.style.top = "-2px"

        // Insert the check mark after the calendar icon
        calendarIcon.parentNode?.insertBefore(
          checkMark,
          calendarIcon.nextSibling
        )
        log.debug("Added check mark after calendar icon")
      } else {
        log.warn("Could not find calendar icon to add check mark")
      }
    } catch (error) {
      log.error("Error adding check mark", error)
    }
  }

  const handleSetETC = async () => {
    if (!etc) {
      log.error("Cannot set ETC: No ETC value provided")
      return
    }

    setIsLoading(true)
    try {
      log.debug(`Setting ETC for service visit ${serviceVisitId}`, {
        etc: etc.format()
      })

      const response = await setETC(serviceVisitId, etc)
      log.debug("API response:", response.data)

      if (response.status === 200) {
        // Only update the Tesla UI field after successful API call
        updateTeslaUIField(etc)

        setApiSuccess(true)
        log.debug(`Successfully set ETC for service visit ${serviceVisitId}`)

        // Reset success state after 3 seconds
        setTimeout(() => {
          setApiSuccess(false)

          // We keep the check mark even after the success state is reset
        }, 3000)
      }
    } catch (error) {
      log.error("Error setting ETC:", error)
      setApiSuccess(false)
    }
    setIsLoading(false)
  }

  return (
    <Box sx={{ mb: 1 }}>
      <Tooltip
        title="Save the calculated ETC to Tesla's system"
        arrow
        placement="top">
        <Button
          fullWidth
          size="small"
          variant="contained"
          color={apiSuccess ? "success" : "primary"}
          onClick={handleSetETC}
          disabled={isLoading || !etc}
          startIcon={
            apiSuccess ? (
              <CheckCircleIcon sx={{ fontSize: "1rem" }} />
            ) : (
              <SaveIcon sx={{ fontSize: "1rem" }} />
            )
          }
          data-testid="set-etc-button"
          sx={{
            fontWeight: 500,
            boxShadow: 2,
            height: 30,
            py: 0.5,
            fontSize: "0.75rem",
            "&.Mui-disabled": {
              backgroundColor: "rgba(25, 118, 210, 0.3)",
              color: "rgba(255, 255, 255, 0.6)"
            },
            "&:hover": {
              boxShadow: 4,
              backgroundColor: apiSuccess ? "success.dark" : "primary.dark"
            }
          }}>
          {isLoading ? (
            <>
              Saving
              <CircularProgress size={12} sx={{ ml: 1, color: "white" }} />
            </>
          ) : apiSuccess ? (
            "Saved!"
          ) : (
            "Set ETC"
          )}
        </Button>
      </Tooltip>
    </Box>
  )
}

export default SetETC
