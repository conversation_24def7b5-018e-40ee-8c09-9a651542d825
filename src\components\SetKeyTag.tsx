import CheckCircleIcon from "@mui/icons-material/CheckCircle"
import DriveEtaIcon from "@mui/icons-material/DriveEta"
import KeyIcon from "@mui/icons-material/Key"
import { Box, Tooltip } from "@mui/material"
import Button from "@mui/material/Button"
import CircularProgress from "@mui/material/CircularProgress"
import dayjs from "dayjs"
import React, { useState } from "react"

import {
  setKeyTag,
  setOwnersTransportationMethod
} from "../services/apiService"

interface SetKeyTagProps {
  keyTag: string
  buttonName: string
  etcDate: dayjs.Dayjs | null
}

const SetKeyTag: React.FC<SetKeyTagProps> = ({
  keyTag,
  buttonName,
  etcDate
}) => {
  const [apiSuccess, setApiSuccess] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const serviceVisitId = window.location.pathname.split("/").pop()

  const handleSetKeyTag = async () => {
    if (!etcDate) {
      console.error("Cannot set key tag: No ETC date provided")
      return
    }

    setIsLoading(true)
    try {
      // Prepare both API calls
      const keyTagPromise = setKeyTag(serviceVisitId!, keyTag)
      const ownersTransportMethodPromise = setOwnersTransportationMethod(
        serviceVisitId!,
        keyTag,
        etcDate
      )

      // Execute both calls simultaneously and wait for both to complete
      const [keyTagResponse, ownersTransportResponse] = await Promise.all([
        keyTagPromise,
        ownersTransportMethodPromise
      ])

      console.log(keyTagResponse.data, ownersTransportResponse.data)

      if (keyTagResponse.status === 200) {
        setApiSuccess(true)

        // Reset success state after 3 seconds
        setTimeout(() => {
          setApiSuccess(false)
        }, 3000)

        // Update the innerText of the specific element when successful
        const targetElement = document.querySelector(
          "#mat-tab-label-1-3 > div > div > span:nth-child(3) > span > a"
        ) as HTMLElement

        if (targetElement) {
          targetElement.innerText = keyTag
        }
      }

      if (ownersTransportResponse.status === 200) {
        const anotherTargetElement = document.querySelector(
          "#mat-tab-content-1-0 > div > div.svd-wrapper.sa-padding-8.sa-padding-top-32.sv-details.ng-star-inserted > div:nth-child(2) > div:nth-child(3) > div > div"
        ) as HTMLElement

        if (anotherTargetElement) {
          if (keyTag === "prepared") {
            anotherTargetElement.innerText = "none 😱"
          } else if (keyTag === "preparedLoaner") {
            anotherTargetElement.innerText = "Loaner 🚗"
          }
        }
      }
    } catch (error) {
      console.error(error)
      setApiSuccess(false)
    }
    setIsLoading(false)
  }

  // Determine icon based on key tag type
  const getIcon = () => {
    if (apiSuccess) {
      return <CheckCircleIcon sx={{ fontSize: "1rem" }} />
    }

    return keyTag === "preparedLoaner" ? (
      <DriveEtaIcon sx={{ fontSize: "1rem" }} />
    ) : (
      <KeyIcon sx={{ fontSize: "1rem" }} />
    )
  }

  // Determine tooltip text
  const getTooltip = () => {
    if (keyTag === "prepared") {
      return "Set key tag to 'prepared' (no loaner)"
    } else if (keyTag === "preparedLoaner") {
      return "Set key tag to 'preparedLoaner' (with loaner)"
    }
    return `Set key tag to '${buttonName}'`
  }

  return (
    <Box sx={{ mb: 1 }}>
      <Tooltip title={getTooltip()} arrow placement="top">
        <Button
          fullWidth
          size="small"
          variant="contained"
          color={
            apiSuccess
              ? "success"
              : keyTag === "preparedLoaner"
              ? "secondary"
              : "info"
          }
          onClick={handleSetKeyTag}
          disabled={isLoading || !etcDate}
          startIcon={getIcon()}
          data-testid={
            keyTag === "preparedLoaner"
              ? "prepared-loaner-button"
              : "prepared-button"
          }
          sx={{
            fontWeight: 500,
            boxShadow: 2,
            height: 30,
            py: 0.5,
            fontSize: "0.75rem",
            textTransform: "capitalize",
            "&.Mui-disabled": {
              backgroundColor:
                keyTag === "preparedLoaner"
                  ? "rgba(156, 39, 176, 0.3)"
                  : "rgba(25, 118, 210, 0.3)",
              color: "rgba(255, 255, 255, 0.6)"
            },
            "&:hover": {
              boxShadow: 4,
              backgroundColor: apiSuccess
                ? "success.dark"
                : keyTag === "preparedLoaner"
                ? "secondary.dark"
                : "info.dark"
            }
          }}>
          {isLoading ? (
            <>
              Setting
              <CircularProgress size={12} sx={{ ml: 1, color: "white" }} />
            </>
          ) : apiSuccess ? (
            "Set!"
          ) : (
            buttonName
          )}
        </Button>
      </Tooltip>
    </Box>
  )
}

export default SetKeyTag
